import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type LoginType = 'employee' | 'leader' | null;

@Injectable({
  providedIn: 'root'
})
export class LoginTypeService {
  private loginTypeSubject = new BehaviorSubject<LoginType>(null);
  public loginType$ = this.loginTypeSubject.asObservable();

  constructor() {
    // Initialize with stored login type
    this.initializeLoginType();
  }

  /**
   * Initialize login type from localStorage
   */
  private initializeLoginType(): void {
    const storedLoginType = localStorage.getItem('loginType') as LoginType;
    if (storedLoginType) {
      this.loginTypeSubject.next(storedLoginType);
    }
  }

  /**
   * Set the login type
   * @param type - The login type ('employee' or 'leader')
   */
  setLoginType(type: LoginType): void {
    this.loginTypeSubject.next(type);
    if (type) {
      localStorage.setItem('loginType', type);
    } else {
      localStorage.removeItem('loginType');
    }
  }

  /**
   * Get the current login type
   * @returns Current login type
   */
  getCurrentLoginType(): LoginType {
    return this.loginTypeSubject.value;
  }

  /**
   * Check if current login is from Leader tab
   * @returns true if logged in via Leader tab
   */
  isLeaderLogin(): boolean {
    return this.getCurrentLoginType() === 'leader';
  }

  /**
   * Check if current login is from UPL Employee tab
   * @returns true if logged in via UPL Employee tab
   */
  isEmployeeLogin(): boolean {
    return this.getCurrentLoginType() === 'employee';
  }

  /**
   * Clear login type (for logout)
   */
  clearLoginType(): void {
    this.setLoginType(null);
  }

  /**
   * Get login type as observable for reactive components
   * @returns Observable of login type
   */
  getLoginType(): Observable<LoginType> {
    return this.loginType$;
  }

  /**
   * Static method to quickly check if current login is from Leader tab
   * This can be used in components without injecting the service
   * @returns true if logged in via Leader tab
   */
  static isLeaderLoginStatic(): boolean {
    return localStorage.getItem('loginType') === 'leader';
  }

  /**
   * Static method to quickly check if current login is from UPL Employee tab
   * This can be used in components without injecting the service
   * @returns true if logged in via UPL Employee tab
   */
  static isEmployeeLoginStatic(): boolean {
    return localStorage.getItem('loginType') === 'employee';
  }

  /**
   * Static method to get current login type
   * This can be used in components without injecting the service
   * @returns Current login type
   */
  static getCurrentLoginTypeStatic(): LoginType {
    return localStorage.getItem('loginType') as LoginType;
  }
}
