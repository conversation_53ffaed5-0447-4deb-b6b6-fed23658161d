import { Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ComponentType, ToastrService } from 'ngx-toastr';
import { GlobalEvents } from '../../helpers/global.events';
import { RewardPointsService } from '../../app-services/reward-points-service';
import { AppConstant } from '../../constants/app.constant';
import * as _ from 'lodash';
import moment from 'moment';
import { BaThemeSpinner } from '../../theme/services/baThemeSpinner/baThemeSpinner.service';
import { AuthenticationHelper } from '../../helpers/authentication';
import { debounceTime, Observable, Subject } from 'rxjs';
import { ngxCsv } from 'ngx-csv';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Utility } from "src/app/shared/utility/utility";
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatPaginatorModule } from '@angular/material/paginator';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { UserService } from 'src/app/app-services/user-service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { TOAST_CONFIG, IndividualConfig } from 'ngx-toastr';
import { NumberFormatService } from 'src/app/shared/shared/number-format.service';

interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  viewDownloadColumnName: string;
  productIcon: boolean;
  noDataMessage: string;
  showEdit: boolean;
  changeStatus: boolean;
  showStatus: boolean;
  showViewDownload: boolean;
  showDelete?: boolean;
}

interface TableData {
  bonusPercentage: number;
  category: null;
  portfolio: string;
  selected: boolean;
  isMatched: boolean;
  productName: string;
  quantity: number;
  unitCost: number;
  amount: number;
  searchBy: string;
  statusName: any[];
  id: string | number | null;
  materialCode: string;
  description: string;
  unit: string;
  importe: number;
  dropdownSettings: any;
  isValid?: boolean; // Add this property
}

interface ProductRequestDTO {
  materialCode: string;
  description: string;
  bonusPercentage: number;
  unit: string;
  quantity: number;
  unitCost: number;
  totalAmount: number;
  category: string;
  databricks_material_nbr: string;
  databricks_material_cd: string;
  portfolio:string;
  databricks_material_desc: string;
  isMatched: boolean | string;
}

@Component({
  selector: 'app-reward-points-history',
  templateUrl: './invoices.html',
  styleUrls: ['./invoices.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: [
    {
      provide: TOAST_CONFIG,
      useValue: {
        timeOut: 3000,
        positionClass: 'toast-top-right',
        preventDuplicates: true,
        closeButton: false,
        progressBar: true,
        maxOpened: 1,
        easing: "ease-in",
        autoDismiss: true,
        toastClass: 'ngx-toastr',
        iconClasses: {
          error: 'toast-error',
          info: 'toast-info',
          success: 'toast-success',
          warning: 'toast-warning'
        }
      } as unknown as IndividualConfig
    }
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatInputModule,
    MatButtonToggleModule,
    NgxPaginationModule,
    MatNativeDateModule,
    DynamicTableComponent,
    MatDatepickerModule,
    MatFormFieldModule,
    MatButtonModule,
    MatPaginatorModule,
    AngularMultiSelectModule,
    MatTableModule,
    MatCheckboxModule,
    MatSelectModule,
    MatFormFieldModule,
  ],
})
export class RewardPointsHistoryComponent implements OnInit {
  @ViewChild('filterMenuDailog') filterMenuDailog!: TemplateRef<any>;
  @ViewChild('addEditSlabDailog') addEditSlabDailog!: TemplateRef<any>;
  @ViewChild('viewProducts') viewProductsTemplate!: TemplateRef<any>;
  @ViewChild('documentViewerTemplate') documentViewerTemplate!: TemplateRef<any>;
  viewProductsDialogRef!: MatDialogRef<any>;
  filterMenuDailogRef!: MatDialogRef<any>;
  rewardsHistoryData: any = [];
  isDistributor: boolean = true;
  isRetailer: boolean = false;
  isForagesDistributor: boolean = true;
  isFcDistributor: boolean = false;
  isForagesRetailer: boolean = false;
  isFcRetailer: boolean = false;
  roductSelectionError: string = '';
  redeemedHistoryFcSeasonDropdownData: any = [];
  redeemedHistoryFCSeason: any = [];
  redeemedHistoryForagesSeasonDropdownData: any = [];
  redeemedHistoryForagesSeason: any = [];
  tableHead: any = [];
  tableColName: any = [];
  previewTableHead: any = [];
  previewTableColName: any = [];
  previewTableData: any = [];
  viewProductHead: any = [];
  viewProductColName: any = [];
  viewProductData: any = [];
  rejectRemark: string = '';
  totalRecordCount: any = [];
  perPage = AppConstant.PER_PAGE_ITEMS;
  showIndex: any = { index: null };
  redeemedMethod: any = [];
  redeemedMethodDetails: any = [];
  selectedSeasonDropdownData: number = 0;
  selectedRedeemedMethod: string = '';
  redeemedHistorySeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Season-Year',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  redeemedHistoryForagesSeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Year',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  redeemedMethodSeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Status',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  dateForm: FormGroup;
  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: true,
    actionsColumnName: 'Actions',
    showViewDownload: true,
    viewDownloadColumnName: 'View/Download',
    noDataMessage: 'No data found',
    showStatus: false,
    showEdit: true,
    changeStatus: false,
    productIcon: false,
    showDelete: true,
  };

  previewConfigurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 1,
    showActionsColumn: false,
    actionsColumnName: 'Actions',
    noDataMessage: 'No data found',
    showStatus: true,
    showEdit: false,
    productIcon: false,
    changeStatus: false,
    showViewDownload: false,
    viewDownloadColumnName: '',
  };

  baseDropdownSettings = {
    text: '',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'itemName',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: true,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  model: any;
  showErrorBlockSection: boolean = false;
  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: true,
    add: false,
    showdropdown1Filters: false,
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
    export: true,
  };
  userButton: any = [];
  custId: any = '1';
  startDate: any;
  endDate: any;
  modelDate: any;
  endDateModel: any = '';
  startDateModel: any = '';
  isDateFilter: boolean = false;
  currentPage: number = 0;
  searchedString: string = '';
  isSearch: boolean = false;

  modelChanged: Subject<string> = new Subject<string>();
  exportData: any = [];
  rewardFilters: any = '';
  date: any;
  currentDate: any;
  cropTab = '';
  firstDay: any;
  formattedFirstDay: any;
  isTable: boolean = true;
  isMap: boolean = false;
  dateData: any;
  toDateAPI!: string;
  fromDateAPI!: string;
  startDateAPI!: string;
  endDateAPI!: string;
  minDate: any;
  maxDate: any;
  dateRange!: FormGroup;
  isFieldCrop: boolean = true;
  isForages: boolean = false;
  userInfo: any;
  userBusinessUnit: any;
  roleId: any;
  regionDataList: any = [];
  zoneDataList: any = [];
  selectedRegion: any = [];
  selectedZone: any = [];
  regionValue: any;
  zoneValue: any;
  addEditSlabDailogRef: MatDialogRef<unknown, any> | undefined;
  @ViewChild('previewDailog') previewDailog!: TemplateRef<any>;
  uploadedFiles: { type: string; name: string; file: File }[] = [];
  @ViewChild('approveInvoice') createLevelTemplate!: TemplateRef<any>;
  createLevelDialogRef!: MatDialogRef<any>;
  @ViewChild('rejectedInvoice') rejectInvoiceTemplate!: TemplateRef<any>;
  rejectInvoiceDialogRef!: MatDialogRef<any>;
  displayedColumns: string[] = ['productName', 'quantity', 'unitCost', 'amount', 'searchBy'];
  dataSource = new MatTableDataSource<TableData>([]);
  DataList: any[] = [];
  filteredDataList: any[] = [];

  formData = {
    xmlFile: null,
    pdfFile: null
  };
  previewData: any;
  statusRegion: any[] = [];
  statusZone: any[] = [];
  statusLeaderName: any[] = [];
  invoiceId: any;
  selectedStatusRegion: string = '';
  selectedStatusZone: string = '';
  selectedStatusLeader: any = [];

  statusDataList: any[] = [];
  status: any = [];
  statusDropdownRegion = {
    text: 'Select Region',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 100,

    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  statusDropdownZone = {
    text: 'Select Zone',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  statusDropdownLeaderName = {
    text: 'Select Leader Name',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'itemName',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  regionDropdownSettings = {
    text: 'Select Region',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false
  };
  zoneDropdownSettings = {
    text: 'Select Zone',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false
  };
  isAdmin: boolean = false;
  userRole: string = '';

  getFilesByType(type: string): { type: string, name: string }[] {
    return this.uploadedFiles.filter(f => f.type === type);
  }

  // REGION
  onRegionSelect(event: any) {
    this.selectedStatusRegion = event.status;
  }
  onRegionDeselect(event: any) {
    this.selectedStatusRegion = '';
  }
  onRegionDeselectAll(event: any) {
    this.selectedStatusRegion = '';
  }

  // ZONE
  onZoneSelect(event: any) {
    this.selectedStatusZone = event.status;
  }
  onZoneDeselect(event: any) {
    this.selectedStatusZone = '';
  }
  onZoneDeselectAll(event: any) {
    this.selectedStatusZone = '';
  }

  // LIDER NAME
  onLeaderSelect(event: any) {
    this.selectedStatusLeader = event;
  }
  onLeaderDeselect(event: any) {
    this.selectedStatusLeader = [];
  }
  onLeaderDeselectAll(event: any) {
    this.selectedStatusLeader = [];
  }




  // uploadedFiles: { name: string, type: string }[] = [];

  // onFileSelected(event: any, fileType: string) {
  //   const file = event.target.files[0];
  //   if (file) {
  //     const validPdf = fileType === 'pdf' && file.type === 'application/pdf';
  //     const validXml = fileType === 'xml' && file.type === 'text/xml';

  //     if (validPdf || validXml) {
  //       this.uploadedFiles.push({ name: file.name, type: fileType });
  //     } else {
  //       alert(`Invalid file type. Please upload a ${fileType.toUpperCase()} file.`);
  //     }
  //   }
  // }

  containers = [
    { icon: '📄', text: 'Upload PDF here', buttonText: 'Upload PDF' },
    { icon: '📄', text: 'Upload XML here', buttonText: 'Upload XML' },
    { icon: '📄', text: 'Upload File here', buttonText: 'Upload File' }
  ];

  // uploadedFiles: {type: string, name: string}[] = [];

  // onFileSelected(event: any, fileType: string) {
  //   const file = event.target.files[0];
  //   if (!file) return;

  //   // Validate file type
  //   const isValidType = 
  //     (fileType === 'pdf' && file.type === 'application/pdf') ||
  //     (fileType === 'xml' && file.type === 'text/xml');

  //   if (!isValidType) {
  //     alert(`Please upload a valid ${fileType.toUpperCase()} file`);
  //     return;
  //   }

  //   // Remove existing file of same type
  //   this.uploadedFiles = this.uploadedFiles.filter(f => f.type !== fileType);

  //   // Add new file
  //   this.uploadedFiles.push({
  //     type: fileType,
  //     name: file.name
  //   });

  //   // Reset input to allow re-selecting same file
  //   event.target.value = '';
  // }


onFileSelected(event: any, fileType: string) {
  const file = event.target.files[0];
  if (!file) return;

  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    this.toastr.error(`File size should not exceed 10MB`);
    event.target.value = '';
    return;
  }

  const isValidType =
    (fileType === 'pdf' && (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf'))) ||
    (fileType === 'xml' && (file.type === 'text/xml' || file.name.toLowerCase().endsWith('.xml')));

  if (!isValidType) {
    this.toastr.error(`Please upload a valid ${fileType.toUpperCase()} file`);
    event.target.value = '';
    return;
  }

  const reader = new FileReader();
  reader.onload = (e: any) => {
    const content = e.target.result as string;

    const suspiciousPattern = fileType === 'pdf'
      ? /\/(JavaScript|Launch|OpenAction|JS)/i
      : /<script\b|<iframe\b|javascript:|onerror=/i;

    if (suspiciousPattern.test(content)) {
      this.toastr.error('Invalid PDF');
      event.target.value = '';
      return;
    }

    this.uploadedFiles = this.uploadedFiles.filter(f => f.type !== fileType);
    this.uploadedFiles.push({
      type: fileType,
      name: file.name,
      file: file
    });

    event.target.value = '';
  };

  reader.readAsText(file.slice(0, 4096));
}



  isFormValid(): boolean {
    const pdfFile = this.uploadedFiles.find(f => f.type === 'pdf');
    const xmlFile = this.uploadedFiles.find(f => f.type === 'xml');
    return !!pdfFile && !!xmlFile && this.statusLeaderName && this.statusLeaderName.length > 0;
  }

  deleteFile(fileType: string) {
    this.uploadedFiles = this.uploadedFiles.filter(f => f.type !== fileType);
  }

  // Add this to your RewardPointsHistoryComponent class
  closePopup() {
    if (this.viewProductsDialogRef) {
      this.viewProductsDialogRef.close();
    }
  }

  submitInvoiceDetails() {
    this._spinner.show();

    const pdfFileObj = this.uploadedFiles.find(f => f.type === 'pdf');
    const xmlFileObj = this.uploadedFiles.find(f => f.type === 'xml');

    if (!pdfFileObj || !xmlFileObj) {
      this.toastr.error('Please upload both PDF and XML files');
      this._spinner.hide();
      return;
    }

    const formData = new FormData();
    formData.append('pdfFile', pdfFileObj.file);
    formData.append('xmlFile', xmlFileObj.file);

    const data = {
      leader: this.selectedStatusLeader
    };

    this.rewardPointService.previewInvoiceDetails(formData, data).subscribe({
      next: (res) => {
        const decrypted = this.utility.decryptString(res);
        const parsedData = JSON.parse(decrypted); // this should now succeed
        this.previewData = parsedData;
        this._spinner.hide();
        this.uploadProductList(parsedData);
        this.getAllProductData(event);

        this.previewTableData = [];
        setTimeout(() => {
          this.dialog.open(this.previewDailog, {
            width: '80%',
            // height: '87%',
            panelClass: 'custom-dialog-container'
          });
          this._spinner.hide();
        }, 100);
      },
      error: (err) => {
        this._spinner.hide();
        const errorMessage = this.utility.decryptErrorMessage(err);
        if (errorMessage) {
          if (errorMessage.toLowerCase().includes('id must not be null')) {
            this.toastr.warning('Please select a product before submitting the invoice.');
          } else {
            this.toastr.error(errorMessage);
          }
        } else {
          this.toastr.error('Something went wrong.');
        }
      }
      
    });
  }

  uploadProductList(res: any): void {
    if (res.products && Array.isArray(res.products)) {
      const formattedData: TableData[] = res.products.map((product: any) => ({
        selected: false,
        isMatched: product.isMatched ?? false,
        productName: this.extractProductName(product),
        quantity: product.quantity ?? 0,
        unitCost: product.unitCost ?? 0,
        amount: product.importe ?? 0,
        searchBy: product.searchBy ?? '',
        statusName: [],
        id: product.id ?? null,
        materialCode: product.materialCode ?? '',
        portfolio:product.portfolio ?? '',
        description: product.description ?? '',
        unit: product.unit ?? '',
        importe: product.importe ?? 0,
        bonusPercentage: product.bonusPercentage ?? 0,
        dropdownSettings: {
          ...this.baseDropdownSettings,
          text: product.isMatched ? 'Product Matched' : 'Select Product',
          searchBy: ['itemName', 'materialCode'],
          showCheckbox: false
        }
      }));

      this.dataSource.data = formattedData;
      this.setPreSelectedValues();
    }
  }

  // Helper method to extract product name from various possible fields
  extractProductName(product: any): string {
    // Check all possible fields where product name might be stored
    if (product.productName && product.productName !== 'null' && product.productName !== 'undefined') {
      return product.productName;
    }

    if (product.databricks_material_desc && product.databricks_material_desc !== 'null' && product.databricks_material_desc !== 'undefined') {
      return product.databricks_material_desc;
    }

    if (product.description && product.description !== 'null' && product.description !== 'undefined') {
      return product.description;
    }

    if (product.name && product.name !== 'null' && product.name !== 'undefined') {
      return product.name;
    }

    // If we have a product code/SKU but no name, show that
    if (product.sku || product.skuCode || product.databricks_material_nbr || product.code) {
      const code = product.sku || product.skuCode || product.databricks_material_nbr || product.code;
      return `Product #${code}`;
    }

    // Last resort
    return 'Unknown Product';
  }

  // Get leader data for dropdown
  getAllLeaderData() {
    const data = {};
    this.rewardPointsService.getAllLeader({}).subscribe(
      (response: any) => {
        let parsedData = typeof response === 'string' ? JSON.parse(response) : response;
        if (Array.isArray(parsedData)) {
          this.DataList = parsedData.map((item: any) => ({
            id: item.id,
            itemName: item.firstName + (item.lastName ? ' ' + item.lastName : ''),
          }));
        }
      },
      (error) => {
        console.error('Failed to fetch leader data', error);
      }
    );
  }

  submitInvoice() {
    this._spinner.show();
    const data = {
      leaderId: this.selectedStatusLeader.id,
      adminId: this.roleId
    }
    const pdfFileObj = this.uploadedFiles.find(f => f.type === 'pdf');
    const xmlFileObj = this.uploadedFiles.find(f => f.type === 'xml');

    if (!pdfFileObj || !xmlFileObj) {
      this.toastr.error('Please upload both PDF and XML files');
      this._spinner.hide();
      return;
    }

    const formData = new FormData();
    formData.append('pdfFile', pdfFileObj.file);
    formData.append('xmlFile', xmlFileObj.file);

    this.rewardPointService.submitInvoiceDetails(formData, data).subscribe({
      next: (res) => {
        // Use setTimeout to ensure UI updates before hiding spinner
        setTimeout(() => {
          this.dialog.closeAll();
          this.toastr.success('Invoice submitted successfully');
          this._spinner.hide();
        }, 100);
      },
      error: (err) => {
        console.error('API Error:', err);
        this.toastr.error('Failed to submit invoice');
        this._spinner.hide();
      }
    });
  }

  // Helper method to format file size
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  constructor(
    private routes: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private rewardPointsService: RewardPointsService,
    public toastr: ToastrService,
    private _spinner: BaThemeSpinner,
    private events: GlobalEvents,
    private rewardService: RewardPointsService,
    private utility: Utility,
    private rewardPointService: RewardPointsService,
    private sidebarService: SidebarServiceService,
    public dialog: MatDialog,
    private userService: UserService,
    private sanitizer: DomSanitizer,
    private numberFormate: NumberFormatService,
  ) {
    this.roleId = localStorage.getItem('roleID');
    this.userInfo = localStorage.getItem('userInfo');
    this.userBusinessUnit = JSON.parse(this.userInfo)?.scanCategory?.id;
    this.events.setChangedContentTopText(
      'Invoice Management (' + this.configurationSettings.totalRecordCount + ')'
    );
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
    this.dateData = moment(new Date()).add(1, 'days');
    this.dateForm = this.fb.group({
      invoiceNumber: [{ value: '', disabled: false }, Validators.required],
      createdDate: [{ value: null, disabled: true }],
      startDatePicker: [this.fromDateAPI],
      endDatePicker: [this.endDateAPI],
      amount: [{ value: '', disabled: false }, Validators.required],

      statusRegion: new FormControl([]), // or null if single select
      statusZone: new FormControl([]),
      statusLeaderName: new FormControl([])

    });
    this.rewardService._tableDialogBox.emit('hide');
  }

  ngOnInit() {
    this._spinner.show();
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
    }
    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
      }
    });
    this.setDateRange();
    this.rewardFilters = 'redeemed';
    this.cropTab = 'FIELD_CROPS';
    window.scrollTo(0, 0);

    this.modelChanged.pipe(debounceTime(400)).subscribe((model: any) => {
      if (model.trim()) {
        this.isSearch = true;
        this.searchedString = model.trim();
        this.getPageData(1);
      } else {
        if (this.isSearch) {
          this.isSearch = false;
          this.searchedString = '';
          this.getPageData(1);
        }
      }
    });
    this.activeTabRoleBased();
    // this.dataSource.data = [
    //   { selected: true, productName: 'ITEM-XYZ123', quantity: 10, unitCost: 50, amount: 500, searchBy: '[4,"Ana López"]', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: false } },
    //   { selected: false, productName: 'ITEM-XYZ123', quantity: 20, unitCost: 75, amount: 1500, searchBy: '', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: true } },
    //   { selected: false, productName: 'ITEM-XYZ123', quantity: 15, unitCost: 60, amount: 900, searchBy: '', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: true } },
    //   { selected: true, productName: 'ITEM-XYZ123', quantity: 25, unitCost: 80, amount: 2000, searchBy: '[36,"Om Maina"]', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: false } },
    //   { selected: true, productName: 'ITEM-XYZ123', quantity: 30, unitCost: 90, amount: 2700, searchBy: '[1,"Aryan Giri"]', statusName: [], dropdownSettings: { ...this.baseDropdownSettings, disabled: false } },
    // ];
    this.getRegion();
    this.invoiceStatus()
  }

  invoiceStatus() {
    this.rewardPointService.getInvoiceStatus().subscribe({
      next: (response: any) => {
        response = this.utility.decryptString(response)
        const status = JSON.parse(response);
        this.redeemedMethodDetails = status.map((item: any) => ({
          id: item,
          name: item,
        }));
        this._spinner.hide();
      },
      error: (error: any) => {
        console.error('Error fetching invoice status:', error);
        this.redeemedMethodDetails = [];
        this._spinner.hide();
      },
    });

  }

  applyDropdownTooltips(): void {
    const selectedTextSpans = document.querySelectorAll(
      '.search-field .selected-list .c-btn span span'
    );

    selectedTextSpans.forEach((el: Element) => {
      const text = el.textContent?.trim() || '';
      el.setAttribute('title', text);
    });
  }

  private setRoleBasedConfig(currentRole: string) {
    this.isAdmin = currentRole === 'ADMIN';
    this.userRole = currentRole;
  }
  openAddSlabDialog(data?: any) {
    // Show spinner before opening dialog
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);
    this.getAllRedeemedMethodData();
    this.addEditSlabDailogRef = this.dialog.open(this.addEditSlabDailog, {
      width: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      data: data,
      hasBackdrop: true,
    });

    // Hide spinner after dialog is fully opened
    this.addEditSlabDailogRef.afterOpened().subscribe(() => {

    });

    this.addEditSlabDailogRef.afterClosed().subscribe(() => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  activeTabRoleBased() {
    this._spinner.show(); // Show spinner when activating tabs

    if (this.userBusinessUnit == 2) {
      this.isFieldCrop = false;
      this.isForages = true;
      this.isDistributor = true;
      this.isRetailer = false;
      // this.getAllScanningCategorys();
      this.tabChanged('isForages');
      this.rewardHistoryTab('isDistributor');
      // this.getAllRedeemedMethodData();
    } else if (this.userBusinessUnit == 1) {
      this.isFieldCrop = true;
      this.isForages = false;
      this.isDistributor = true;
      this.isRetailer = false;
      this.tabChanged('isFieldCrop');
      this.rewardHistoryTab('isDistributor');
      // this.getAllScanningCategorys();
      // this.getAllRedeemedMethodData();
    } else if (this.roleId == 1 && this.userBusinessUnit == null) {
      this.isDistributor = true;
      this.isRetailer = false;
      this.rewardHistoryTab('isDistributor');
      // this.getAllRedeemedMethodData();
      // this.getAllScanningCategorys();
    }
    // The spinner will be hidden by the methods called above
  }

  ngOnDestroy() {
    this.modelChanged.unsubscribe();
  }

  setDateRange() {
    this.dateRange = this.fb.group({
      start: [''],
      end: [''],
    });
  }

  /**
   * Called for creating a date object
   * @param date
   * @returns {{date: {year: number, month: number, day: (number|Date)}}}
   */
  setDateObject(date: any) {
    if (date) {
      const dateObject = {
        date: {
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          day: date.getDate(),
        },
      };
      return dateObject;
    } else {
      return null;
    }
  }

  /**
   * Method for setting the headers of the table for different customer types
   */
  setTableHeader() {
    this.tableHead = [
      'Leader Name',
      'Invoice Number',
      'Date',
      'Region',
      'Zone',
      'Distributor',
      'Uploaded By',
      'Uploaded On',
      'Details',
      'Total Bonification (Mex$)',
      'Status',
    ];
    this.tableColName = [
      'leadername',
      'invoicenumber',
      'date',
      'region',
      'zone',
      'distributorname',
      'uploadedby',
      'uploadedon',
      'product',
      'totalbonification',
      'status',
    ];
    this.previewTableHead = [
      'Product Name',
      'RFC (ID)',
      'SKU',
      'Quantity',
      'Total Amount',
    ];
    this.previewTableColName = [
      'productname',
      'rfcid',
      'sku',
      'quantity',
      'totalamount',
    ];
    this.viewProductHead = [
      'Product Name',
      'Quantity',
      'Unit Cost  (Mex$)',
      'Total cost (Mex$)',
      'Segment',
      'Portfolio',
    ];
    this.viewProductColName = [
      'productname',
      'quantity',
      'unitcost',
      'amount',
      'category',
      'portfolio',
    ];
    this.rewardsHistoryData = [];
    this.getAllRedeemedAmountHistoryData();
  }

  tabChanged(tabChangeEvent: any): void {
    this._spinner.show(); // Show spinner when changing tab
    this.cropTab = tabChangeEvent;
    switch (tabChangeEvent) {
      case 'isFieldCrop':
        this.isFieldCrop = true;
        this.isForages = false;
        this.currentPage = 0;
        this.selectedSeasonDropdownData = 0;
        this.redeemedHistoryForagesSeason = [];
        this.redeemedHistoryFCSeason = [];
        this.searchedString = '';
        this.model = '';
        this.startDate = '';
        this.endDate = '';
        this.startDateModel = '';
        this.endDateModel = '';
        this.fromDateAPI = '';
        this.toDateAPI = '';
        this.startDateAPI = '';
        this.endDateAPI = '';
        this.selectedRedeemedMethod = '';
        this.redeemedMethod = [];
        this.setTableHeader();
        break;
      case 'isForages':
        this.isFieldCrop = false;
        this.isForages = true;
        this.currentPage = 0;
        this.selectedSeasonDropdownData = 0;
        this.redeemedHistoryForagesSeason = [];
        this.redeemedHistoryFCSeason = [];
        this.searchedString = '';
        this.model = '';
        this.startDate = '';
        this.endDate = '';
        this.startDateModel = '';
        this.endDateModel = '';
        this.fromDateAPI = '';
        this.toDateAPI = '';
        this.startDateAPI = '';
        this.endDateAPI = '';
        this.selectedRedeemedMethod = '';
        this.redeemedMethod = [];
        this.setTableHeader();
        // this.getAllScanningCategorys();
        break;
      default:
        this.rewardFilters = 'redeemed';
        this.isFieldCrop = true;
        this.isForages = false;
        this.currentPage = 0;
        this.searchedString = '';
        this.model = '';
        this.selectedRedeemedMethod = '';
        this.redeemedMethod = [];
        this.router.navigate(['/invoices']);
        this.setTableHeader();
        break;
    }
  }

  /**
   * Method for changing customer tabs
   * @param data
   */
  userRoleRewardPoints: string = 'isDistributor';
  rewardHistoryTab(data: any) {
    this._spinner.show(); // Show spinner when changing tab
    switch (data) {
      case 'isDistributor':
        this.rewardFilters = 'redeemed';
        this.isDistributor = true;
        this.isRetailer = false;
        this.currentPage = 0;
        this.selectedSeasonDropdownData = 0;
        this.redeemedHistoryForagesSeason = [];
        this.redeemedHistoryFCSeason = [];
        this.searchedString = '';
        this.model = '';
        this.router.navigate(['/invoices']);
        this.startDate = '';
        this.endDate = '';
        this.startDateModel = '';
        this.endDateModel = '';
        this.fromDateAPI = '';
        this.toDateAPI = '';
        this.startDateAPI = '';
        this.endDateAPI = '';
        this.dateForm.controls['startDatePicker'].setValue('');
        this.dateForm.controls['endDatePicker'].setValue('');
        this.redeemedMethod = [];
        this.selectedRedeemedMethod = '';
        this.setTableHeader();
        break;
      case 'isRetailer':
        this.rewardFilters = 'redeemed';
        this.isDistributor = false;
        this.isRetailer = true;
        this.currentPage = 0;
        this.redeemedMethod = [];
        this.selectedRedeemedMethod = '';
        this.selectedSeasonDropdownData = 0;
        this.redeemedHistoryForagesSeason = [];
        this.redeemedHistoryFCSeason = [];
        this.searchedString = '';
        this.model = '';
        this.router.navigate(['/invoices']);
        this.startDate = '';
        this.endDate = '';
        this.startDateModel = '';
        this.endDateModel = '';
        this.fromDateAPI = '';
        this.toDateAPI = '';
        this.startDateAPI = '';
        this.endDateAPI = '';
        this.dateForm.controls['startDatePicker'].setValue('');
        this.dateForm.controls['endDatePicker'].setValue('');
        this.setTableHeader();
        break;
      default:
        this.rewardFilters = 'redeemed';
        this.isDistributor = true;
        this.isRetailer = false;
        this.isFieldCrop = true;
        this.isForages = false;
        this.currentPage = 0;
        this.searchedString = '';
        this.model = '';
        this.selectedRedeemedMethod = '';
        this.redeemedMethod = [];
        this.router.navigate(['/invoices']);
        this.setTableHeader();
        break;
    }
  }

  /**
   * Called to get the data according to the searched query
   * @param event
   */
  onSearch(event: any) {
    this._spinner.show(); // Show spinner when searching
    this.modelChanged.next(event);
  }

  /**
   * Method for clearing search query
   */
  clearSearch() {
    this._spinner.show(); // Show spinner when clearing search
    this.searchedString = '';
    this.model = '';
    this.isSearch = false;
    this.getAllRedeemedAmountHistoryData();
  }

  filterDropdown(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);
    this.filterMenuDailogRef = this.dialog.open(this.filterMenuDailog, {
      width: '18%',
      height: 'auto',
      position: {
        top: '12%',
        right: '5%',
      },
      backdropClass: 'custom-backdrop',
      panelClass: 'filter-dialog-container',
      data: data,
      autoFocus: false,
      disableClose: false,
      hasBackdrop: true,
    });
    this.filterMenuDailogRef.afterClosed().subscribe((result: any) => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  clearFilter() {
    this._spinner.show(); // Show spinner when clearing filters
    this.selectedRegion = [];
    this.regionValue = '';
    this.selectedZone = [];
    this.zoneValue = '';
    this.selectedRedeemedMethod = '';
    this.redeemedMethod = [];
    this.filterMenuDailogRef.close();
    this.getAllRedeemedAmountHistoryData();
  }

  filterApply() {
    this._spinner.show(); // Show spinner when applying filters
    if (this.startDate && !this.endDate) {
      this.toastr.warning('Please select end date');
      this._spinner.hide(); // Hide spinner if validation fails
    } else
      if (!this.startDate && !this.endDate) {
        this.getAllRedeemedAmountHistoryData();
        this.filterMenuDailogRef.close();
      }
      else if (this.startDate && this.endDate) {
        this.getAllRedeemedAmountHistoryData();
        this.filterMenuDailogRef.close();
      }
  }

  /**
   * Method for select season
   */
  onRedeemedHistorySeasonSelected(event: any) {
    this._spinner.show(); // Show spinner when selecting season
    this.selectedSeasonDropdownData = event.id;
    this.getAllRedeemedAmountHistoryData();
  }

  /**
   * Method for deselect season
   */
  onRedeemedHistorySeasonDeSelected(event: any) {
    this.selectedSeasonDropdownData = 0;
    this.getAllRedeemedAmountHistoryData();
  }

  /**
   * Method for deselect All season
   */

  onRedeemedHistorySeasonDeSelectedAll(event: any) {
    this.selectedSeasonDropdownData = 0;
    this.getAllRedeemedAmountHistoryData();
  }

  /**
   * Method for select redeemed method
   */
  onRedeemedMethodHistorySelected(event: any) {
    this.selectedRedeemedMethod = event.id;
  }

  /**
   * Method for deselect redeemed method
   */
  onRedeemedMethodHistoryDeSelected(event: any) {
    this.selectedRedeemedMethod = '';
    this.getAllRedeemedAmountHistoryData();
  }

  /**
   * Method for deselect season
   */
  onRedeemedMethodHistoryDeSelectedAll(event: any) {
    this.selectedRedeemedMethod = '';
    this.getAllRedeemedAmountHistoryData();
  }

  getAllRedeemedAmountHistoryData(page?: any) {
    this._spinner.show(); // Show spinner when fetching data
    const VIEW_ICON_PATH = './assets/img/product.svg';
    let data = {
      pageLimit: this.perPage,
      currentPage: page ? page : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      startDate: this.fromDateAPI ? this.fromDateAPI : '',
      endDate: this.toDateAPI ? this.toDateAPI : '',
      scanCategoryId: this.isFieldCrop ? 1 : 2,
      customerTypeId: this.isDistributor ? 1 : 2,
      scanningCategoryId: this.selectedSeasonDropdownData
        ? this.selectedSeasonDropdownData
        : 0,
      status: this.selectedRedeemedMethod,
      transferType: this.selectedRedeemedMethod
        ? this.selectedRedeemedMethod
        : '',
      unPaged: false,
      regionId: this.selectedRegion ? this.selectedRegion : '',
      zoneId: this.selectedZone ? this.selectedZone : '',
    };



    this.rewardPointsService.getAllInvoice(data).subscribe({
      next: (response: any) => {
        try {
          let parsedResponse = response;

          // Parse if response is a JSON string
          if (typeof response === 'string') {
            parsedResponse = JSON.parse(response);
          }

          // Proceed if encryptedBody is present
          if (parsedResponse?.encryptedBody) {
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            const invoiceData = JSON.parse(decrypted);

            this.rewardsHistoryData = [];

            if (invoiceData?.content?.length) {
              invoiceData.content.forEach((schemeInfo: any) => {
                this.rewardsHistoryData.push({
                  invoicenumber: schemeInfo?.invoiceNumber
                    ? this.utility.formatString(schemeInfo.invoiceNumber)
                    : 'NA',
                  date: schemeInfo?.invoiceDate
                    ? this.utility.formatString(
                      new Date(schemeInfo.invoiceDate).toISOString().split('T')[0]
                    )
                    : 'NA',
                  region: schemeInfo?.region
                    ? this.utility.formatString(schemeInfo.region)
                    : 'NA',
                  zone: schemeInfo?.zone
                    ? this.utility.formatString(schemeInfo.zone)
                    : 'NA',
                  leadername: schemeInfo?.leaderName
                    ? this.utility.toUpperCaseUtil(schemeInfo.leaderName)
                    : 'NA',
                  distributorname: schemeInfo?.distributor
                    ? this.utility.toUpperCaseUtil(schemeInfo.distributor)
                    : 'NA',
                  id: schemeInfo?.id ?? 'NA',
                  uploadedby: schemeInfo?.uploadedBy
                    ? this.utility.capitalizeWords(schemeInfo.uploadedBy)
                    : 'NA',
                  uploadedon: schemeInfo?.uploadedOn
                    ? this.utility.formatString(
                      new Date(schemeInfo.uploadedOn).toISOString().split('T')[0]
                    )
                    : 'NA',
                  totalbonification: schemeInfo?.totalBonification != null
                    ? this.numberFormate.formatNumber(schemeInfo.totalBonification)
                    : 'NA',                  
                  product: VIEW_ICON_PATH,
                  status: schemeInfo?.status
                    ? this.utility.formatString(schemeInfo.status)
                    : '',
                  invoiceFile: schemeInfo?.pdfFile || schemeInfo?.invoiceFile || null,
                });
              });

              // Set totals and update UI
              this.totalRecordCount = invoiceData.totalElements;
              this.configurationSettings.totalRecordCount = this.totalRecordCount;
              this.events.setChangedContentTopText(`Invoice Management (${this.totalRecordCount})`);
            } else {
              this.totalRecordCount = 0;
              this.events.setChangedContentTopText('Invoice Management (0)');
            }
          }
        } catch (err) {
          console.error('Error processing invoice data:', err);
          this.totalRecordCount = 0;
          this.events.setChangedContentTopText('Invoice Management (0)');
        } finally {
          this._spinner.hide();
        }
      },
      error: (err) => {
        console.error('API Error:', err);
        this._spinner.hide();
        this.events.setChangedContentTopText('Invoice Management (0)');
        this.totalRecordCount = 0;
      },
    });

  }

  endDateChanged(event: any) {
    if (!this.endDate) {
      this.toastr.warning('Please select end date');
    } else {
      this.fromDateAPI = moment(this.startDate).format('YYYY-MM-DD');
      this.toDateAPI = moment(this.endDate).format('YYYY-MM-DD');
    }
  }

  /**
   * To handle the scannig category dropdown
   */
  getAllScanningCategorys() {
    let data = {
      scanCategoryId: this.isFieldCrop ? 1 : 2,
    };
    this.rewardPointsService.getAllScannedCategoryData(data).subscribe({
      next: (seasonDetails: any) => {
        seasonDetails = JSON.parse((seasonDetails));
        if (this.isFieldCrop) {
          this.redeemedHistoryFcSeasonDropdownData = [];
          seasonDetails.forEach((seasonData: any) => {
            let seasonDataObj = {
              name: seasonData.season.name
                ? this.utility.formatString(seasonData.season.name) +
                ' - ' +
                seasonData.scanYear
                : '',
              id: seasonData.id ? seasonData.id : '',
            };
            this.redeemedHistoryFcSeasonDropdownData.push(seasonDataObj);
          });
        } else {
          this.redeemedHistoryForagesSeasonDropdownData = [];
          seasonDetails.forEach((seasonData: any) => {
            let seasonDataObj = {
              name: seasonData.scanYear,
              id: seasonData.id,
            };
            this.redeemedHistoryForagesSeasonDropdownData.push(seasonDataObj);
          });
        }
      },
      error: (errorResponse: any) => {
        let error: any = (errorResponse.error)
        error = JSON.parse(error);
        if ('Full authentication is required to access this resource' == error.message) {
          localStorage.clear();
          this.router.navigate(['']);
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  getAllRedeemedMethodData() {
    this._spinner.show();
    this.rewardPointsService.getAllLeader({}).subscribe({
      next: (response: any) => {
        let parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);
        if (Array.isArray(parsedData)) {
          const filteredLeaders = parsedData.filter(item => item.targetStatus === true);
          this.statusDataList = filteredLeaders.map((item: any) => ({
            id: item.id,
            itemName: item.firstName + (item.lastName ? ' ' + item.lastName : ''),
          }));
        }
        // Use setTimeout to ensure UI updates before hiding spinner
        setTimeout(() => {
          this._spinner.hide();
        }, 100);
      },
      error: (error) => {
        console.error('Failed to fetch leader data', error);
        this.toastr.error('Failed to load leader data');
        this._spinner.hide();
      }
    });
  }


  /**
   * API call for getting the redeemed history data
   * @param page
   */
  exportAllRedeemedAmountHistoryData(page?: any) {
    let data = {
      pageLimit: AppConstant.PER_PAGE_ITEMS,
      currentPage: this.currentPage ? this.currentPage : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      startDate: this.fromDateAPI ? this.fromDateAPI : '',
      endDate: this.toDateAPI ? this.toDateAPI : '',
      scanCategoryId: this.isFieldCrop ? 1 : 2,
      customerTypeId: this.isDistributor ? 1 : 2,
      scanningCategoryId: this.selectedSeasonDropdownData
        ? this.selectedSeasonDropdownData
        : 0,
      transferType: this.selectedRedeemedMethod
        ? this.selectedRedeemedMethod
        : '',
      unPaged: true,
    };

    const rewards =
      this.rewardPointsService.exportAllInvoice(data);
    rewards.subscribe({
      next: (exportsDataRes: any) => {
        exportsDataRes = this .utility.decryptString(exportsDataRes)
        exportsDataRes = JSON.parse((exportsDataRes));
        this.exportData = [];
        if (
          exportsDataRes &&
          exportsDataRes.length
        ) {
          exportsDataRes.forEach((schemeInfo: any) => {
            const schemeInfoObj = {
              leadername: schemeInfo?.leaderName
                ? this.utility.toUpperCaseUtil(schemeInfo.leaderName)
                : 'NA',
              invoicenumber: schemeInfo?.invoiceNumber
                ? schemeInfo.invoiceNumber
                : 'NA',
              date: schemeInfo?.invoiceDate
                ? this.utility.formatString(new Date(schemeInfo.invoiceDate).toISOString().split('T')[0])
                : 'NA',
              region: schemeInfo?.region
                ? this.utility.formatString(schemeInfo.region)
                : 'NA',
              zone: schemeInfo?.zone
                ? this.utility.formatString(schemeInfo.zone)
                : 'NA',
              distributorname: schemeInfo?.distributor
                ? this.utility.toUpperCaseUtil(schemeInfo.distributor)
                : 'NA',
              uploadedby: schemeInfo?.uploadedBy
                ? this.utility.capitalizeWords(schemeInfo.uploadedBy)
                : 'NA',
              uploadedon: schemeInfo?.uploadedOn
                ? this.utility.formatString(new Date(schemeInfo.uploadedOn).toISOString().split('T')[0])
                : 'NA',
              totalbonification: schemeInfo?.totalBonification
                ? this.numberFormate.formatNumber(schemeInfo.totalBonification)
                : 'NA',
              status: schemeInfo.status
                ? this.utility.formatString(schemeInfo.status)
                : 'NA',
              productName: schemeInfo.productName
                ? this.utility.formatString(schemeInfo.productName)
                : 'NA',
              quantity: schemeInfo.quantity
                ? schemeInfo.quantity
                : 'NA',
              unitCost: schemeInfo.unitCost
                ? this.numberFormate.formatNumber(schemeInfo?.unitCost)
                : 'NA',
              amount: schemeInfo.amount
                ? this.numberFormate.formatNumber(schemeInfo.amount)
                : 'NA',
              bonusPercentage: schemeInfo.bonusPercentage
                ? this.numberFormate.formatPercentage(schemeInfo.bonusPercentage)
                : 'NA',
              bonification: schemeInfo.bonification
                ? this.numberFormate.formatNumber(schemeInfo.bonification)
                : 'NA',
            };
            this.exportData.push(schemeInfoObj);
          });
          let options = {
            fieldSeparator: ',',
            quoteStrings: '"',
            decimalseparator: '.',
            showLabels: true,
            headers: [
              'Leader Name',
              'Invoice Number',
              'Date',
              'Region',
              'Zone',
              'Distributor',
              'Uploaded By',
              'Uploaded On',
              'Total Bonification (Mex$)',
              'Status',
              'Product Name',
              'Quantity',
              'Unit Cost  (Mex$)',
              'Total cost (Mex$)',
              'Bonus Percentage',
              'Bonification',
            ],
          };
          new ngxCsv(
            this.exportData,
            'Invoice Management Details',
            options
          );
        } else {
          this.toastr.warning('No data available');
        }

      },
      error: (errorResponse: any) => {
        let error: any = (errorResponse.error)
        error = JSON.parse(error);
        if ('Full authentication is required to access this resource' == error.message) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  /**
   * To handle the page change event
   * @param page
   */
  getPageData(page: any) {
    this._spinner.show(); // Show spinner when changing page
    this.currentPage = page - 1;
    this.configurationSettings.currentPage = page;
    this.getAllRedeemedAmountHistoryData(this.currentPage);
    // this._spinner.hide();
  }

  /**
   * called when start date filter is changed
   * @param event
   */
  onStartDateChanged(event: any) {
    if (event && event.formatted) {
      this.startDate = '';
      this.endDate = '';
      this.endDateModel = '';
      this.startDate = moment(event.formatted).format(
        'YYYY-MM-DD'
      );
      const momentDate = moment(this.startDate, 'YYYY-MM-DD').subtract(
        1,
        'day'
      );
    } else {
      this.startDate = '';
    }
  }
  startdate2: any;
  /**
   * called when end date filter is changed
   * @param event
   */
  onEndDateChanged(event: any) {
    if (event && event.formatted) {
      this.endDate = '';
      this.endDate = moment(event.formatted).format('YYYY-MM-DD');
    } else {
      this.endDate = '';
    }
  }

  formatDate(date: Date): string {
    return date.toISOString();
  }
  /**
   * Method for getting the filtered data by the start date and end date
   */
  filterSchemeByDate(rangePicker: any) {
    this.fromDateAPI = moment(this.startDate).format(
      'YYYY-MM-DD'
    );
    this.toDateAPI = moment(this.endDate).format('YYYY-MM-DD');
  }

  /**
   * Called to clear the date filters
   */
  clearSchemeFilter() {
    this.startDate = '';
    this.endDate = '';
    this.startDateModel = '';
    this.endDateModel = '';
    this.fromDateAPI = '';
    this.toDateAPI = '';
    this.startDateAPI = '';
    this.endDateAPI = '';
    this.dateForm.controls['startDatePicker'].setValue('');
    this.dateForm.controls['endDatePicker'].setValue('');

    if (this.isDateFilter) {
      this.isDateFilter = false;
      this.rewardsHistoryData = [];
      this.currentPage = 1;
      this.getAllRedeemedAmountHistoryData();
    }
  }

  /**
   * Triggered when user clicks on the scanned and redeemed radio button
   */
  changeFilter(event: any) {
    this._spinner.show(); // Show spinner when changing filter
    this.startDate = '';
    this.endDate = '';
    this.startDateModel = '';
    this.endDateModel = '';
    this.fromDateAPI = '';
    this.toDateAPI = '';
    this.startDateAPI = '';
    this.endDateAPI = '';
    this.dateForm.controls['startDatePicker'].setValue('');
    this.dateForm.controls['endDatePicker'].setValue('');
    if (event.value) {
      if (this.isDistributor) {
        event.value === 'redeemed'
          ? this.rewardHistoryTab('isRedeemedDistributor')
          : this.rewardHistoryTab('isFcDistributor');
      } else if (this.isRetailer) {
        event.value === 'redeemed'
          ? this.rewardHistoryTab('isForagesRetailer')
          : this.rewardHistoryTab('isFcRetailer');
      }
    }
  }

  /**
   * Method to change the view between table and map
   * @param data
   */
  viewChange(data: any) {
    switch (data) {
      case 'isTable':
        this.isTable = true;
        this.isMap = false;
        break;
      case 'isMap':
        this.isTable = false;
        this.isMap = true;
    }
  }
  cleanCustomAndCallOnDate(val: any, event: any) {
    let month = event.getMonth() + 1;
    let day = event.getDate();
    const year = event.getFullYear();
    if (day < 10) {
      day = '0' + day;
    }
    if (month < 10) {
      month = '0' + month;
    }
    const date = day + '-' + month + '-' + year;
    switch (val) {
      case 'from-date':
        this.fromDateAPI = date;
        break;
      case 'to-date':
        this.toDateAPI = date;
        break;
    }

    this.startDateAPI = this.fromDateAPI;
    this.endDateAPI = this.toDateAPI;
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
  }

  dateValidation(fromDateVal: any, toDateVal: any) {
    const fromDate = fromDateVal === undefined ? '' : fromDateVal.split('-');
    let toDate = toDateVal === undefined ? '' : toDateVal.split('-');
    const fromDateValue = fromDate[0] + fromDate[1] + fromDate[2];
    const toDateValue = toDate[0] + toDate[1] + toDate[2];
    if (fromDateValue > toDateValue) {
      this.toastr.error(
        'Invalid date. Start Date should be less than End Date'
      );
      return;
    } else {
      this.startDate = this.fromDateAPI;
      this.endDate = this.toDateAPI;
    }
  }
  funRestEmail(event: any) {
    var k;
    k = event.charCode;
    if (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      event.key == '.' ||
      event.key == '-' ||
      event.key == '_' ||
      event.key == '+' ||
      event.key == '@' ||
      event.key == '' ||
      event.keyCode == 13
    ) {
    } else {
      this.toastr.error(event.key + ' ' + 'not allowed');
    }

    return (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      event.key == '.' ||
      event.key == '-' ||
      event.key == '_' ||
      event.key == '+' ||
      event.key == '@' ||
      (event.key == '' && event.key !== 'Enter')
    );
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
      // this.toastr.error('Double space is not allowed');
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
      // Allow uppercase letters, lowercase letters, backspace, space, and numbers
    } else {
      event.preventDefault();
      // this.toastr.error(event.key + ' ' + 'not allowed');
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  submitForm() {
    this.dialog.open(this.previewDailog, {
      width: '80%',
      // height: '87%',
      panelClass: 'custom-dialog-container'
    });

  }

  onCancelPreview() {
    this.dialog.closeAll();
    this.addEditSlabDailogRef = this.dialog.open(this.addEditSlabDailog, {
      width: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
    });


  }

  //edit products
  openAddProductsDialog(data?: any) {
    this._spinner.show();
    if (!this.createLevelTemplate) {
      console.error('createLevelTemplate is undefined!');

      return;
    }

    this.createLevelDialogRef = this.dialog.open(this.createLevelTemplate, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
    });

    this.createLevelDialogRef.afterOpened().subscribe(() => {
      this._spinner.hide();
      // // Hide spinner after dialog is opened
    });

    this.createLevelDialogRef.afterClosed().subscribe(() => {
      // Clean up if needed
      this._spinner.hide();
    });
  }

  openRejectInvoiceDialog(data?: any) {

    this.rejectInvoiceDialogRef = this.dialog.open(this.rejectInvoiceTemplate, {
      width: '40%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
    });

    this.rejectInvoiceDialogRef.afterClosed().subscribe(() => {
    });
  }

  onDataSelect(event: any, row: TableData) {
    row.statusName = [event];
    this.validateCheckedRows();
    setTimeout(() => this.applyDropdownTooltips(), 100);
  }

  onDataDeselect(event: any, row: TableData) {
    row.statusName = [];
    this.validateCheckedRows();
  }

  onDataDeselectAll(event: any, row: TableData) {
    row.statusName = [];
    this.validateCheckedRows();
  }



  getAllProductData(event: any) {
    this._spinner.show();
    this.rewardPointsService.getProductByID({ id: event.id }).subscribe({
      next: (response: any) => {
        const parsedData = typeof response === 'string' ? JSON.parse(this.utility.decryptString(response)) : JSON.parse(response);

        if (Array.isArray(parsedData)) {
          this.DataList = parsedData.map((item: any) => ({

            id: item.id || item.databricks_material_cd,
            itemName: item.databricks_material_desc || item.description,
            materialCode: item.databricks_material_cd || '',
            bonusPercentage: item.bonusPercentage || 0,
            materialNumber: item.databricks_material_nbr,
            category: item.category || '',
            portfolio:item.portfolio || '',
            
          }));
          this.setPreSelectedValues(); // Update preselections after data load
        }
        this._spinner.hide();
      },
      error: (error) => {
        this.toastr.error('Failed to load products');
        this._spinner.hide();
      }
    });
  }

  setPreSelectedValues() {
    this.dataSource.data.forEach(row => {
      if (row.searchBy) {
        try {
          const match = row.searchBy.match(/\[(\d+),\s*"([^"]+)"\]/);
          if (match) {
            const matchedItem = this.DataList.find(item =>
              item.id === parseInt(match[1]) &&
              item.itemName === match[2]
            );

            if (matchedItem) {
              row.statusName = [matchedItem];
              row.isMatched = true; // Set matched flag
            }
          }
        } catch (error) {
          console.error('Error parsing searchBy:', error);
        }
      }

      // Disable if matched or not selected
      row.dropdownSettings = {
        ...row.dropdownSettings,
        text: row.isMatched ? 'Product Matched' : 'Select Product'
      };
    });
    this.validateCheckedRows();
  }

  cancelReject() {
    this.rejectRemark = '';
    this.rejectInvoiceDialogRef.close();
  }

  close() {
    this.createLevelDialogRef.close();

  }

  submitUploadData() {
    this._spinner.show();
    const leaderId = this.selectedStatusLeader?.id;

    if (!leaderId) {
      this.toastr.error('Please select a leader');
      this._spinner.hide();
      return;
    }

    const pdfFileObj = this.uploadedFiles.find(f => f.type === 'pdf');
    const xmlFileObj = this.uploadedFiles.find(f => f.type === 'xml');

    if (!pdfFileObj || !xmlFileObj) {
      this.toastr.error('Please upload both PDF and XML files');
      this._spinner.hide();
      return;
    }

    const formData = new FormData();

    formData.append('pdfFile', pdfFileObj.file);
    formData.append('xmlFile', xmlFileObj.file);

    const allProducts = this.dataSource.data.map(product => {
      const isSelected = !!product.selected;
      const selectedProduct = product.statusName?.[0];

      const baseProduct = {
        isMatched: false,
        quantity: product.quantity ?? 0,
        unitCost: product.unitCost ?? 0,
        totalAmount: product.importe ?? 0,
        materialCode: product.materialCode ?? '',
        bonusPercentage: 0,
        description: product.description ?? '',
        unit: product.unit ?? '',
        category: 'uncategorized',
        databricks_material_desc: '',
        databricks_material_cd: '',
        databricks_material_nbr: '',
        portfolio:'',
      };

      if (isSelected && selectedProduct) {
        baseProduct.isMatched = true;
        baseProduct.databricks_material_desc = selectedProduct.itemName ?? '';
        baseProduct.databricks_material_cd = selectedProduct.materialCode ?? '';
        baseProduct.portfolio = selectedProduct.portfolio ?? '';
        baseProduct.category = selectedProduct.category ?? 'uncategorized';
        baseProduct.bonusPercentage = selectedProduct.bonusPercentage ?? 0;

      }

      return baseProduct;
    });

    const encrypted = this.utility.encryptString(allProducts); // returns { encryptedBody }
    const encryptedProducts = typeof encrypted === 'object' && 'encryptedBody' in encrypted
      ? encrypted.encryptedBody
      : encrypted;

    formData.append('products', encryptedProducts);

    this.rewardPointsService.submitInvoiceWithProducts(formData, leaderId).subscribe({
      next: (res) => {
        setTimeout(() => {
          this.dialog.closeAll();
          this.toastr.success('Invoice and products saved successfully');
          this.previewTableData = [];
          this._spinner.hide();
          this.getAllRedeemedAmountHistoryData();
          this.selectedStatusLeader = [];
          this.statusLeaderName = [];
          this.uploadedFiles = [];
          this.formData = {
            xmlFile: null,
            pdfFile: null
          };
        }, 100);
      },
      error: (err) => {
        let errorMessageForToast: string = 'Something went wrong while saving the invoice.';
        let rawErrorDetails: any = err.error;
        try {
          const parsedErrorObject = JSON.parse(rawErrorDetails);
          if (parsedErrorObject && typeof parsedErrorObject.encryptedBody === 'string') {
            const encryptedPayload = parsedErrorObject.encryptedBody;
            const decryptedContent = this.utility.decryptStringError(encryptedPayload);
            try {
              const finalErrorDetails = JSON.parse(decryptedContent);
              errorMessageForToast = finalErrorDetails.message || finalErrorDetails.detail || JSON.stringify(finalErrorDetails);
              console.error('API Error (decrypted JSON):', finalErrorDetails);
            } catch (parseDecryptedError) {
              errorMessageForToast = decryptedContent;
              console.error('API Error (decrypted text):', decryptedContent);
            }
          } else {
            errorMessageForToast = rawErrorDetails;
            console.error('API Error (non-encrypted string):', rawErrorDetails);
          }

        } catch (parseOrDecryptError) {
          console.error('Error handling API response:', parseOrDecryptError);
          if (err.message && err.message.includes("400 OK")) {
            errorMessageForToast = `Server responded with 400 Bad Request. ${err.message}`;
          } else {
            errorMessageForToast = 'An unexpected error occurred during error processing.';
          }
        } finally {
          this.toastr.error(errorMessageForToast);
          this._spinner.hide();
        }
      }
    });
  }



  submitReject() {
    if (!this.invoiceId || !this.rejectRemark.trim()) {
      this.toastr.warning('Please enter a comment');
      return;
    }

    this._spinner.show();
    this.rewardPointsService.rejectInvoice(this.invoiceId, this.rejectRemark).subscribe({
      next: (response: any) => {
        this._spinner.hide();
        this.toastr.success("Invoice rejected successfully");
        this.rejectInvoiceDialogRef.close();
        this.getAllRedeemedAmountHistoryData();
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error);
        try {
          error = JSON.parse(error);
          this.toastr.error(error.message || "Failed to reject Invoice");
        } catch (e) {
          this.toastr.error("Failed to reject Invoice");
        }
      }
    });
  }

  submit() {
    if (!this.isFormValid) {
      this.toastr.error('Please select a product');
      return;
    }

    this._spinner.show();

    const hasSelectedRows = this.dataSource.data.some(row => row.selected);

    const allProducts = this.dataSource.data.map(row => {
      const isSelected = !!row.selected;
      const hasStatusName = Array.isArray(row.statusName) && row.statusName.length > 0;
      const selectedProduct = hasStatusName ? row.statusName[0] : null;

      if (isSelected) {
        if (!selectedProduct) {
          // Selected row with no product chosen
          return {

            // databricks_material_desc: row.productName || null,
            // selected: true,
            isMatched: row.isMatched,
          };
        }

        // Selected row with product chosen
        return {
          id: row.id,
          databricks_material_desc: selectedProduct.itemName,
          databricks_material_cd: selectedProduct.materialCode,
          portfolio:selectedProduct. portfolio,
          bonusPercentage: selectedProduct.bonusPercentage,
          category: selectedProduct.category,
          isMatched: true
        };
      }

      // Preserve existing data for unselected rows (including original matched data)
      return {
        id: row.id,
        // databricks_material_desc: row.databricks_material_desc, // From API
        // databricks_material_cd: row.databricks_material_cd,     // From API
        // databricks_material_nbr: row.databricks_material_nbr,   // From API
        // category: row.category,                                 // From API
        isMatched: row.isMatched // Retain original value
      };
    });

    // Check if any selected row lacks a product selection
    // const hasSelectedRowsWithoutProducts = allProducts.some(product =>
    //   product.selected && !product.isMatched
    // );

    // if (hasSelectedRowsWithoutProducts) {
    //   this.toastr.warning('Please select products from the dropdown for each selected row');
    //   this._spinner.hide();
    //   return;
    // }

    this.rewardPointsService.saveInvoiceProducts(allProducts).subscribe({
      next: (response) => {
        setTimeout(() => {
          this.toastr.success('Products saved successfully');
          this.close();
          this.getAllRedeemedAmountHistoryData();
          this._spinner.hide();
        }, 100);
      },
      error: (error) => {
        let errorMessage = 'Failed to save products';
        let rawErrorDetails: any = error.error;

        try {
          // Attempt to parse the error if it's encrypted
          const parsedErrorObject = JSON.parse(rawErrorDetails);
          if (parsedErrorObject && typeof parsedErrorObject.encryptedBody === 'string') {
            const encryptedPayload = parsedErrorObject.encryptedBody;
            const decryptedContent = this.utility.decryptStringError(encryptedPayload);

            try {
              const finalErrorDetails = JSON.parse(decryptedContent);
              errorMessage = finalErrorDetails.message || finalErrorDetails.detail || JSON.stringify(finalErrorDetails);
              console.error('API Error (decrypted JSON):', finalErrorDetails);
            } catch (parseDecryptedError) {
              errorMessage = decryptedContent;
              console.error('API Error (decrypted text):', decryptedContent);
            }
          } else {
            errorMessage = rawErrorDetails;
            console.error('API Error (non-encrypted string):', rawErrorDetails);
          }
        } catch (parseOrDecryptError) {
          console.error('Error handling API response:', parseOrDecryptError);
          if (error.message && error.message.includes("400 OK")) {
            errorMessage = `Server responded with 400 Bad Request. ${error.message}`;
          } else {
            errorMessage = 'An unexpected error occurred during error processing.';
          }
        } finally {
          this.toastr.error(errorMessage);
          this._spinner.hide();
        }
      }
    });
  }


  // Checkbox Change Handler
  onCheckboxChange(row: TableData) {
    // Prevent any changes if the row is matched
    if (row.isMatched) {
      this.toastr.warning('Matched products cannot be modified');
      return;
    }

    // Toggle selection
    row.selected = !row.selected;

    // Update dropdown disabled state
    row.dropdownSettings = {
      ...row.dropdownSettings,
      disabled: !row.selected || row.isMatched
    };

    // Clear selection if unchecking
    if (!row.selected) {
      row.statusName = [];
    }

    // Check if any row is checked but has no product selected
    this.validateCheckedRows();
  }


  emitInvoiceEdit(event: any) {
    this.openAddProductsDialog();
    this.getProductData(event);
    this.getAllProductData(event);
  }
  rejectInvoice(event: any) {
    this.openRejectInvoiceDialog(event);
    this.invoiceId = event.id
  }

  viewProduct(event: any) {
    this.openViewProduct(event);
  }

  openViewProduct(event: any) {
    this._spinner.show(); // Show spinner before opening dialog

    this.viewProductsDialogRef = this.dialog.open(this.viewProductsTemplate, {
      width: '60%',
      disableClose: false,
      panelClass: 'custom-popup',
      hasBackdrop: true,
      autoFocus: false  
    });

    this.viewProductsDialogRef.afterOpened().subscribe(() => {
      this.getProductData(event);
    });

    this.viewProductsDialogRef.backdropClick().subscribe(() => {
      this.closePopup();
    });

    this.viewProductsDialogRef.afterClosed().subscribe(() => {
    });
  }

  getProductData(event: any): void {
    this._spinner.show();
    this.viewProductData = [];
    this.dataSource.data = [];

    if (!event?.id) {
      this.toastr.error('Invalid product data');
      this._spinner.hide();
      return;
    }

    this.rewardPointsService.getAllProductById(event.id).subscribe({
      next: (response: any) => {
        response = this.utility.decryptString(response)
        try {
          const productList = typeof response === 'string' ? JSON.parse(response) : response;

          if (Array.isArray(productList) && productList.length > 0) {
            const formattedViewData = [];
            const formattedTableData = [];

            for (const item of productList) {
              const isMatched = item.isMatched;

              formattedViewData.push({
                id: item?.id ?? 'NA',

                productname: this.utility.formatString(item?.productName) ?? 'NA',
                quantity: item?.quantity ?? 'NA',
                category: this.utility.toUpperCaseUtil(item?.category) ?? "NA",
                portfolio: this.utility.capitalizeWords (item?.portfolio) ?? 'NA',
                unitcost: this.utility.formatUSD (item?.unitCost) ?? 'NA',
                amount: this.utility.formatUSD (item?.amount) ?? 'NA',
                bonuspercentage: item?.bonusPercentage ?? 'NA',
                bonification: this.utility.formatString(item?.bonification) ?? 'NA',
                isMatched: isMatched
              });

              formattedTableData.push({
                selected: isMatched,
                isMatched: isMatched,
                productName: item?.productName ?? 'NA',
                quantity: item?.quantity ?? 0,
                unitCost: item?.unitCost ?? 0,
                amount: item?.amount ?? 0,
                searchBy: item?.searchBy ?? '',
                statusName: isMatched && item?.matchedProduct ? [item.matchedProduct] : [],
                id: item?.id ?? null,
                dropdownSettings: {
                  ...this.baseDropdownSettings,
                  disabled: true,
                  searchBy: ['itemName', 'materialCode']
                },
                // Add missing properties
                category: item?.category ?? null,
                materialCode: item?.materialCode ?? '',
                description: item?.description ?? '',
                unit: item?.unit ?? '',
                importe: item?.importe ?? 0,
                bonusPercentage: item?.bonusPercentage ?? 0,
                portfolio: item?.portfolio ?? '',
              });
            }

            this.viewProductData = formattedViewData;
            this.dataSource.data = formattedTableData;

            if (formattedTableData.some(item => item.selected)) {
              this.setPreSelectedValues();
            }
          } else {
            this.toastr.warning('No product data available');
          }
        } catch (error) {
          console.error('Error parsing product data:', error);
          this.toastr.error('Error processing product data');
        } finally {
          this._spinner.hide();
        }
      },
      error: (err) => {
        console.error('Failed to fetch product data', err);
        this.toastr.error('Failed to load product data');
        this._spinner.hide();
      }
    });
  }
  onViewInvoice(data: any) {
    const fileName = data.invoiceFile || data.pdfFile;

    if (!fileName) {
      this.toastr.warning('No invoice file available to view');
      return;
    }

    this._spinner.show();

    // Call the service to get the document URL
    this.userService.showDocument(fileName).subscribe({
      next: (response) => {
        if (response && response.url) {
          // Set the iframe URL
          this.iframeUrl = response.url;

          // Open the document viewer dialog
          this.openDocumentViewer();
        } else {
          this.toastr.error('Failed to load invoice file');
        }
        this._spinner.hide();
      },
      error: (error) => {
        console.error('Error viewing invoice:', error);
        this.toastr.error('Failed to load invoice file');
        this._spinner.hide();
      }
    });
  }
  onDownloadInvoice(data: any) {
    if (!data || !data.invoiceFile) {
      this.toastr.warning('No invoice file available to download');
      return;
    }
    this._spinner.show();
    const fileName = data.invoiceFile;
    this.userService.showDocument(fileName).subscribe({
      next: (response) => {
        if (response && response.url) {

          // Create a link element to download the file
          const link = document.createElement('a');
          link.href = response.url;
          link.download = fileName || 'invoice.pdf';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } else {
          this.toastr.error('Failed to download invoice file');
        }
        this._spinner.hide();
      },
      error: (error) => {
        console.error('Error downloading invoice:', error);
        this.toastr.error('Failed to download invoice file');
        this._spinner.hide();
      }
    });
  }


  uploadClose() {
    this.addEditSlabDailogRef?.close();
    this.selectedStatusLeader = [];
    this.statusLeaderName = [];
    this.uploadedFiles = [];
    this.formData = {
      xmlFile: null,
      pdfFile: null
    };
  }
  iframeUrl: string | null = null;
  currentDocumentTitle: string = 'Invoice Document';

  openDocumentViewer() {
    const documentViewerTemplate = this.dialog.open(this.documentViewerTemplate, {
      width: '90%',
      height: '90%',
      panelClass: 'document-viewer-dialog',
      disableClose: false
    });

    documentViewerTemplate.afterClosed().subscribe(() => {
      this.iframeUrl = null;
    });
  }

  getSafeUrl(url: string | null): SafeResourceUrl {
    if (!url) {
      return this.sanitizer.bypassSecurityTrustResourceUrl('about:blank');
    }
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  closeDocumentViewer() {
    this.dialog.closeAll();
    this.iframeUrl = null;
  }

  private _isFormValid: boolean = true;
  get formValid(): boolean {
    return this._isFormValid;
  }

  validateCheckedRows() {
    const hasSelectedRowsWithoutProducts = this.dataSource.data.some(row =>
      row.selected && (!row.statusName || row.statusName.length === 0)
    );
    this._isFormValid = !hasSelectedRowsWithoutProducts;
  }
  getRegion() {
    this.regionDataList = [];
    this.userService.getRegion().subscribe(
      (response: any) => {
        let rest = this.utility.decryptString(response);
        let leaderArray = JSON.parse(rest);
        this.regionDataList = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
      },
      (error) => {
        console.error(' Failed to fetch redeemed method data', error);
      }
    );
  }

  getZoneByID(id: any) {
    const data = {
      zoneId: id
    }; // if needed, add request params here
    this.zoneDataList = [];

    this.userService.getZoneById(data).subscribe(
      (response: any) => {
        let leaderArray = JSON.parse(this.utility.decryptString(response));
        this.zoneDataList = leaderArray.map((item: any) => ({
          id: item.id,
          name: item.name,
          code: item.code,
        }));
      },
      (error) => {
        console.error(' Failed to fetch redeemed method data', error);
      }
    );
  }

  selectRegion(event: any) {
    this.selectedRegion = event.id;
    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';

    this.getZoneByID(this.selectedRegion);
  }

  deselectionRegion(event: any) {
    this.selectedRegion = [];
    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';
  }

  deselectionAllRegion(event: any) {
    this.deselectionRegion(event);
    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';
  }
  selectZone(event: any) {
    this.selectedZone = event.id;
  }

  deselectionZone(event: any) {
    this.selectedZone = [];
  }

  deselectionAllZone(event: any) {
    this.deselectionZone(event);
  }
}
