import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-leader-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="leader-dashboard-container">
      <div class="welcome-section">
        <h1 class="welcome-title">Welcome to Leader Dashboard</h1>
        <p class="welcome-subtitle">Your personalized dashboard is coming soon!</p>
      </div>
      
      <div class="content-section">
        <div class="placeholder-card">
          <div class="card-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3>Dashboard Features</h3>
          <p>We're working on exciting new features specifically designed for leaders like you.</p>
        </div>
        
        <div class="placeholder-card">
          <div class="card-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 11H15M9 15H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L19.7071 9.70711C19.8946 9.89464 20 10.149 20 10.4142V19C20 20.1046 19.1046 21 18 21H17Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3>Reports & Analytics</h3>
          <p>Comprehensive reporting tools will be available here soon.</p>
        </div>
        
        <div class="placeholder-card">
          <div class="card-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21M12.5 7C12.5 9.20914 10.7091 11 8.5 11C6.29086 11 4.5 9.20914 4.5 7C4.5 4.79086 6.29086 3 8.5 3C10.7091 3 12.5 4.79086 12.5 7ZM20 8V14M23 11H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3>Team Management</h3>
          <p>Manage your team and track performance metrics.</p>
        </div>
      </div>
      
      <div class="coming-soon-section">
        <div class="coming-soon-badge">
          <span>🚀 Coming Soon</span>
        </div>
        <p>Stay tuned for more exciting features!</p>
      </div>
    </div>
  `,
  styles: [`
    .leader-dashboard-container {
      padding: 2rem;
      max-width: 1200px;
      margin: 0 auto;
      min-height: 80vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
    }

    .welcome-section {
      margin-bottom: 3rem;
    }

    .welcome-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .welcome-subtitle {
      font-size: 1.2rem;
      color: #7f8c8d;
      margin-bottom: 0;
    }

    .content-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      width: 100%;
      margin-bottom: 3rem;
    }

    .placeholder-card {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid #e1e8ed;
    }

    .placeholder-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-icon {
      color: #667eea;
      margin-bottom: 1rem;
      display: flex;
      justify-content: center;
    }

    .placeholder-card h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1rem;
    }

    .placeholder-card p {
      color: #7f8c8d;
      line-height: 1.6;
      margin: 0;
    }

    .coming-soon-section {
      margin-top: 2rem;
    }

    .coming-soon-badge {
      display: inline-block;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 25px;
      font-weight: 600;
      margin-bottom: 1rem;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .coming-soon-section p {
      color: #7f8c8d;
      font-size: 1.1rem;
    }

    @media (max-width: 768px) {
      .leader-dashboard-container {
        padding: 1rem;
      }

      .welcome-title {
        font-size: 2rem;
      }

      .content-section {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .placeholder-card {
        padding: 1.5rem;
      }
    }
  `]
})
export class LeaderDashboardComponent implements OnInit {

  constructor() { }

  ngOnInit(): void {
    // Any initialization logic for leader dashboard
  }
}
