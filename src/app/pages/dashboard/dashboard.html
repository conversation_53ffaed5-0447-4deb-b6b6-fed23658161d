
<!-- Conditional Dashboard Content -->
<div *ngIf="isLeaderLogin">
  <!-- Leader Dashboard -->
  <app-leader-dashboard></app-leader-dashboard>
</div>

<div *ngIf="!isLeaderLogin" class="dashboard-container">
  <!-- Filters Section -->
  <div class="dashboard-filters">
    <div class="filter-container">
      <div class="filter-row">
        <!-- Region Filter -->
        <div class="filter-item">
          <label>Region</label>
          <app-custom-dropdown [options]="regions" [selectedId]="selectedRegion" [placeholder]="'All'"
            [dropdownType]="'region'" [dropdownId]="'region-dropdown'"
            (selectionChange)="onSelectionChange($event)"></app-custom-dropdown>
        </div>

        <!-- Zone Filter -->
        <!-- <div class="filter-item">
          <app-custom-dropdown
            [options]="zones"
            [selectedId]="selectedZone"
            [placeholder]="'Select Zone'"
            [dropdownType]="'zone'"
            [dropdownId]="'zone-dropdown'"
            (selectionChange)="onSelectionChange($event)"
          ></app-custom-dropdown>
        </div> -->

        <!-- Fiscal Year Filter -->
        <div class="filter-item">
          <label>Fiscal Year</label>
          <app-custom-dropdown [options]="fiscalYears" [selectedId]="selectedFY" [placeholder]="'Select Fiscal Year'"
            [dropdownType]="'fy'" [dropdownId]="'fy-dropdown'"
            (selectionChange)="onSelectionChange($event)"></app-custom-dropdown>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Dashboard Content -->
  <div class="dashboard-content">
    <!-- Top Tables Section -->
    <div class="dashboard-tables">
      <!-- Top Regions Table -->
      <!-- Top Regions Table -->
      <div class="table-container">
        <h3 class="table-title">Top Regions</h3>
        <div class="table-scroll-container">
          <table class="data-table">
            <thead>
              <tr>
                <th class="sort-header" [class.active]="regionSortColumn === 'name'" (click)="sortRegionsData('name')">
                  <span>Region</span>
                  <span *ngIf="regionSortColumn === 'name'" class="sort-icon">
                    {{ regionSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="regionSortColumn === 'convenioFY24'" (click)="sortRegionsData('convenioFY24')">
                  <span>Total Amount(Mex$)</span>
                  <span *ngIf="regionSortColumn === 'convenioFY24'" class="sort-icon">
                    {{ regionSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="regionSortColumn === 'avanceCrop'" (click)="sortRegionsData('avanceCrop')">
                  <span>Achieved CP(Mex$)</span>
                  <span *ngIf="regionSortColumn === 'avanceCrop'" class="sort-icon">
                    {{ regionSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="regionSortColumn === 'avanceNPP'" (click)="sortRegionsData('avanceNPP')">
                  <span>Achieved NPP(Mex$)</span>
                  <span *ngIf="regionSortColumn === 'avanceNPP'" class="sort-icon">
                    {{ regionSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="regionSortColumn === 'avanceAmount'" (click)="sortRegionsData('avanceAmount')">
                  <span>Achieved Total Amount(Mex$)</span>
                  <span *ngIf="regionSortColumn === 'avanceAmount'" class="sort-icon">
                    {{ regionSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="regionSortColumn === 'achievedTotalPercentage'" (click)="sortRegionsData('achievedTotalPercentage')">
                  <span>Achieved Percentage</span>
                  <span *ngIf="regionSortColumn === 'achievedTotalPercentage'" class="sort-icon">
                    {{ regionSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
              </tr>
            </thead>

            <tbody>
              <ng-container *ngIf="topRegions?.length; else noRegionData">
                <tr *ngFor="let region of topRegions">
                  <td>{{ region.name }}</td>
                  <td class="currency-cell">{{ region.convenioFY24 || 0  }}</td>
                  <td class="currency-cell">{{ region.avanceCrop   }}</td>
                  <td class="currency-cell">{{ region.avanceNPP  }}</td>
                  <td class="currency-cell">{{ region.avanceAmount  }}</td>
                  <td class="currency-cell">{{ region.achievedTotalPercentage  }}</td>
                </tr>
                <tr class="total-row">
                  <td>Addition</td>
                  <td class="currency-cell">{{ totalRegions.convenioFY24 || 0  }}</td>
                  <td class="currency-cell">{{ totalRegions.avanceCrop || 0  }}</td>
                  <td class="currency-cell">{{ totalRegions.avanceNPP || 0  }}</td>
                  <td class="currency-cell">{{ totalRegions.avanceAmount || 0  }}</td>
                  <td class="currency-cell">{{ totalRegions.achievedTotalPercentage || 0  }}</td>
                </tr>
              </ng-container>
              <ng-template #noRegionData>
                <tr>
                  <td colspan="5" class="text-center">No data found</td>
                </tr>
              </ng-template>
            </tbody>
          </table>

        </div>
      </div>


      <!-- Top Zone Table -->
      <div class="table-container">
        <h3 class="table-title">Top 10 Zones of {{ selectedRegionName ? selectedRegionName : 'all' }} Region</h3>
        <div class="table-scroll-container">
          <table class="data-table">
            <thead>
              <tr>
                <th class="sort-header" [class.active]="zoneSortColumn === 'name'" (click)="sortZonesData('name')">
                  Zone
                  <span *ngIf="zoneSortColumn === 'name'" class="sort-icon">
                    {{ zoneSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="zoneSortColumn === 'convenioFY24'" (click)="sortZonesData('convenioFY24')">
                  Total Amount(Mex$)
                  <span *ngIf="zoneSortColumn === 'convenioFY24'" class="sort-icon">
                    {{ zoneSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="zoneSortColumn === 'avanceCrop'" (click)="sortZonesData('avanceCrop')">
                  Achieved CP(Mex$)
                  <span *ngIf="zoneSortColumn === 'avanceCrop'" class="sort-icon">
                    {{ zoneSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="zoneSortColumn === 'avanceNPP'" (click)="sortZonesData('avanceNPP')">
                  Achieved NPP(Mex$)
                  <span *ngIf="zoneSortColumn === 'avanceNPP'" class="sort-icon">
                    {{ zoneSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="zoneSortColumn === 'avanceAmount'" (click)="sortZonesData('avanceAmount')">
                  Achieved Total Amount(Mex$)
                  <span *ngIf="zoneSortColumn === 'avanceAmount'" class="sort-icon">
                    {{ zoneSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngIf="topZones?.length; else noData">
                <tr *ngFor="let zone of topZones">
                  <td>{{ zone.name }}</td>
                  <td class="currency-cell">{{ zone.convenioFY24 || 0  }}</td>
                  <td class="currency-cell">{{ zone.avanceCrop || 0  }}</td>
                  <td class="currency-cell">{{ zone.avanceNPP || 0  }}</td>
                  <td class="currency-cell">{{ zone.avanceAmount || 0  }}</td>
                </tr>
                <tr class="total-row">
                  <td>Addition</td>
                  <td class="currency-cell">{{ totalZones.convenioFY24 || 0  }}</td>
                  <td class="currency-cell">{{ totalZones.avanceCrop || 0  }}</td>
                  <td class="currency-cell">{{ totalZones.avanceNPP || 0  }}</td>
                  <td class="currency-cell">{{ totalZones.avanceAmount || 0  }}</td>
                </tr>
              </ng-container>
              <ng-template #noData>
                <tr>
                  <td colspan="5" class="text-center">No data found</td>
                </tr>
              </ng-template>
            </tbody>
          </table>

        </div>
      </div>
    </div>

    <!-- Regional Progress + Leaders Table -->
    <div class="regional-progress-container">
      <!-- Chart Section -->
      <div class="chart-section">
        <h3 class="table-title">Zonal Progress Summary</h3>
        <div class="chart-and-legend">
          <highcharts-chart [Highcharts]="Highcharts" [options]="chartOptions"
            style="width: 100% !important; height: 100% !important;"></highcharts-chart>
        </div>
      </div>

      <!-- Leaders Table Section -->
      <div class="leaders-table-container">
        <h3 class="table-title">Top 10 Leaders</h3>
        <div class="table-scroll-container">
          <table class="data-table leaders-table">
            <thead>
              <tr>
                <th class="sort-header" [class.active]="leaderSortColumn === 'index'" (click)="sortLeadersData('index')">
                  Sr.No
                  <span *ngIf="leaderSortColumn === 'index'" class="sort-icon">
                    {{ leaderSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="leaderSortColumn === 'name'" (click)="sortLeadersData('name')">
                  Name
                  <span *ngIf="leaderSortColumn === 'name'" class="sort-icon">
                    {{ leaderSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="leaderSortColumn === 'assignedTarget'" (click)="sortLeadersData('assignedTarget')">
                  Assigned Target(Mex$)
                  <span *ngIf="leaderSortColumn === 'assignedTarget'" class="sort-icon">
                    {{ leaderSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="leaderSortColumn === 'achievedTarget'" (click)="sortLeadersData('achievedTarget')">
                  Achieved Target(Mex$)
                  <span *ngIf="leaderSortColumn === 'achievedTarget'" class="sort-icon">
                    {{ leaderSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
                <th class="sort-header" [class.active]="leaderSortColumn === 'achievedBonus'" (click)="sortLeadersData('achievedBonus')">
                  Achieved Bonus Amount (Mex$)
                  <span *ngIf="leaderSortColumn === 'achievedBonus'" class="sort-icon">
                    {{ leaderSortDirection === 'asc' ? '▲' : '▼' }}
                  </span>
                </th>
              </tr>
            </thead>

            <tbody>
              <ng-container *ngIf="topLeaders?.length; else noLeaderData">
                <tr *ngFor="let leader of topLeaders; let i = index">
                  <td>{{ leaderSortColumn === 'index' ? (leaderSortDirection === 'asc' ? i + 1 : topLeaders.length - i) : i + 1 }}.</td>
                  <td>{{ (leader.name | uppercase) || 0 }}</td>
                  <td class="currency-cell">{{ leader.assignedTarget || 0 }}</td>
                  <td class="currency-cell">{{ leader.achievedTarget || 0 }}</td>
                  <td class="currency-cell">{{ leader.achievedBonus || 0 }}</td>
                </tr>
              </ng-container>
               <ng-template #noLeaderData>
                <tr>
                  <td colspan="5" class="text-center">No data found</td>
                </tr>
              </ng-template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- End of Employee Dashboard -->
