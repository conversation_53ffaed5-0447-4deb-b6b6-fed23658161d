import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';

import { Utility } from '../../../shared/utility/utility';
import { UserService } from '../../../app-services/user-service';
import { EmailValidator } from '../../../theme/validators/email.validator';

@Component({
  selector: 'app-leader-signup',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, AngularMultiSelectModule],
  templateUrl: './leader-signup.component.html',

  styleUrls: ['./leader-signup.component.scss']
})
export class LeaderSignupComponent implements OnInit {
  @Output() close = new EventEmitter<void>();

  currentStep = 1;
  step1Form!: FormGroup;
  step2Form!: FormGroup;
  uploadedFiles: { [key: string]: File } = {};
  mobileOTPSent = false;
  emailOTPSent = false;
  mobileOTPVerified = false;
  emailOTPVerified = false;

  documentTypes = [
    { key: 'rfcDoc', name: 'RFC Document', description: 'Upload RFC Document (PDF, JPG, PNG up to 10MB)' },
    { key: 'idDoc', name: 'Identity Document', description: 'Upload Identity Document (PDF, JPG, PNG up to 10MB)' },
    { key: 'addressDoc', name: 'Address Document', description: 'Upload Address Document (PDF, JPG, PNG up to 10MB)' }
  ];

  // Dropdown data arrays
  regionDataList: any[] = [];
  zoneDataList: any[] = [];
  stateDataList: any[] = [];
  cityDataList: any[] = [];
  cropDataList: any[] = [];

  // Selected values for multiselect dropdowns
  selectedRegion: any = [];
  selectedZone: any = [];
  selectedState: any = [];
  selectedCity: any = [];
  selectedCrop: any = [];

  // Dropdown settings (same as user management)
  regionDropdownSettings = {
    text: 'Select Region',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'name',
    primaryKey: 'id',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150, // ✅ Dynamic height for better scrolling
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
    enableCheckAll: false,
    selectAllText: '',
    unSelectAllText: '',
    itemsShowLimit: 1,
    allowSearchFilter: true,
    noDataLabel: 'No regions found',
    searchPlaceholderText: 'Search'
  };

  zoneDropdownSettings = {
    text: 'Select Zone',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'name',
    primaryKey: 'id',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150, // ✅ Dynamic height for better scrolling
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
    enableCheckAll: false,
    selectAllText: '',
    unSelectAllText: '',
    itemsShowLimit: 1,
    allowSearchFilter: true,
    noDataLabel: 'No zones found',
    searchPlaceholderText: 'Search'
  };

  stateDropdownSettings = {
    text: 'Select State',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'name',
    primaryKey: 'id',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150, // ✅ Dynamic height for better scrolling
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
    enableCheckAll: false,
    selectAllText: '',
    unSelectAllText: '',
    itemsShowLimit: 1,
    allowSearchFilter: true,
    noDataLabel: 'No states found',
    searchPlaceholderText: 'Search'
  };

  cityDropdownSettings = {
    text: 'Select City',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'name',
    primaryKey: 'id',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150, // ✅ Dynamic height for better scrolling
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
    enableCheckAll: false,
    selectAllText: '',
    unSelectAllText: '',
    itemsShowLimit: 1,
    allowSearchFilter: true,
    noDataLabel: 'No cities found',
    searchPlaceholderText: 'Search'
  };

  cropDropdownSettings = {
    text: 'Select Main Crop',
    enableSearchFilter: true,
    classes: 'myclass customer-dd',
    labelKey: 'name',
    primaryKey: 'id',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150, // ✅ Dynamic height for better scrolling
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
    enableCheckAll: false,
    selectAllText: '',
    unSelectAllText: '',
    itemsShowLimit: 1,
    allowSearchFilter: true,
    noDataLabel: 'No crops found',
    searchPlaceholderText: 'Search'
  };

  constructor(
    private fb: FormBuilder,
    private toastr: ToastrService,
    private utility: Utility,
    private userService: UserService
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {
    // Load initial dropdown data
    this.getRegion();
  }

  initializeForms(): void {
    this.step1Form = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2), Validators.pattern(/^[a-zA-Z0-9\s]+$/)]],
      lastName: ['', [Validators.required, Validators.minLength(2), Validators.pattern(/^[a-zA-Z0-9\s]+$/)]],
      cellphone: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      email: ['', [Validators.required, EmailValidator.validate]],
      companyName: ['', [Validators.required, Validators.minLength(2), Validators.pattern(/^[a-zA-Z0-9\s]+$/)]],
      mobileOTP: [''],
      emailOTP: ['']
    });

    this.step2Form = this.fb.group({
      address: ['', [Validators.required, Validators.minLength(5)]],
      region: ['', [Validators.required]],
      zone: ['', [Validators.required]],
      state: ['', [Validators.required]],
      city: ['', [Validators.required]],
      mainCrop: ['', [Validators.required]]
    });
  }

  onBack(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.close.emit();
    }
  }

  onNext(): void {
    if (this.currentStep < 3) {
      this.currentStep++;
    } else {
      this.onSubmit();
    }
  }

  isCurrentStepValid(): boolean {
    switch (this.currentStep) {
      case 1:
        const basicFieldsValid = (this.step1Form.get('firstName')?.valid ?? false) &&
                                (this.step1Form.get('lastName')?.valid ?? false) &&
                                (this.step1Form.get('cellphone')?.valid ?? false) &&
                                (this.step1Form.get('email')?.valid ?? false) &&
                                (this.step1Form.get('companyName')?.valid ?? false);

        // Check if OTP is sent and verified
        const mobileOTPValid = !this.mobileOTPSent || (this.mobileOTPVerified && (this.step1Form.get('mobileOTP')?.valid ?? false));
        const emailOTPValid = !this.emailOTPSent || (this.emailOTPVerified && (this.step1Form.get('emailOTP')?.valid ?? false));

        return basicFieldsValid && mobileOTPValid && emailOTPValid;
      case 2:
        return this.step2Form.valid;
      case 3:
        return Object.keys(this.uploadedFiles).length === 3 &&
               !!this.uploadedFiles['rfcDoc'] &&
               !!this.uploadedFiles['idDoc'] &&
               !!this.uploadedFiles['addressDoc'];
      default:
        return false;
    }
  }

  sendOTP(type: 'mobile' | 'email'): void {
    const value = type === 'mobile' ? this.step1Form.get('cellphone')?.value : this.step1Form.get('email')?.value;

    if (!value) {
      this.toastr.error(`Please enter ${type === 'mobile' ? 'mobile number' : 'email'} first`);
      return;
    }

    // Use the exact same payload structure as login component (but with isSignup: true)
    const payload = type === 'email' ?
      { email: value, isSignup: true } :
      { mobileNo: value, isSignup: true };

    // Use the exact same UserService methods as login component
    const apiCall = type === 'email' ?
      this.userService.generateEmailOTPForLogin(payload) :
      this.userService.generateMobileOTPForLogin(payload);

    apiCall.subscribe({
      next: (response: any) => {
        console.log('Signup OTP Generation Response:', response);

        try {
          // First try to parse the response directly
          let result;
          try {
            result = typeof response === 'string' ? JSON.parse(response) : response;
          } catch (directParseError) {
            // If direct parsing fails, try decryption
            const decrypted = this.utility.decrypt(response.encryptedBody || response);
            result = JSON.parse(decrypted);
          }

          // Check if API response indicates success
          if (result.success === false || result.error === true) {
            // API returned error - show backend message and stop
            this.toastr.error(result.message || `Failed to send OTP to ${type}. Please retry!`);
            return;
          }

          // Success case
          this.toastr.success(`OTP sent to your ${type} successfully!`);

          if (type === 'mobile') {
            this.mobileOTPSent = true;
            this.step1Form.get('mobileOTP')?.setValidators([Validators.required, Validators.pattern(/^\d{6}$/)]);
            this.step1Form.get('mobileOTP')?.updateValueAndValidity();
          } else {
            this.emailOTPSent = true;
            this.step1Form.get('emailOTP')?.setValidators([Validators.required, Validators.pattern(/^\d{6}$/)]);
            this.step1Form.get('emailOTP')?.updateValueAndValidity();
          }
        } catch (error) {
          console.log('Failed to parse signup OTP generation response:', error);
          // If all parsing fails, assume success for backward compatibility
          this.toastr.success(`OTP sent to your ${type} successfully!`);

          if (type === 'mobile') {
            this.mobileOTPSent = true;
            this.step1Form.get('mobileOTP')?.setValidators([Validators.required, Validators.pattern(/^\d{6}$/)]);
            this.step1Form.get('mobileOTP')?.updateValueAndValidity();
          } else {
            this.emailOTPSent = true;
            this.step1Form.get('emailOTP')?.setValidators([Validators.required, Validators.pattern(/^\d{6}$/)]);
            this.step1Form.get('emailOTP')?.updateValueAndValidity();
          }
        }
      },
      error: (error: any) => {
        console.log('Signup OTP Generation Error:', error);

        try {
          // Parse the error.error string to get the encryptedBody
          const errorData = typeof error.error === 'string' ? JSON.parse(error.error) : error.error;

          if (errorData && errorData.encryptedBody) {
            // Try to decrypt the encrypted error response
            try {
              const decryptedErr = this.utility.decryptStringError(errorData.encryptedBody);
              const parsedErr = JSON.parse(decryptedErr);
              // Show the actual error message from backend
              this.toastr.error(parsedErr.message || parsedErr.error || `Failed to send OTP to ${type}`);
              return;
            } catch (decryptError) {
              console.log('Decryption failed:', decryptError);
              // Try alternative decryption method
              try {
                const alternativeDecrypted = this.utility.decryptErrorMessage(error);
                this.toastr.error(alternativeDecrypted || `Failed to send OTP to ${type}`);
                return;
              } catch (altError) {
                console.log('Alternative decryption failed:', altError);
              }
            }
          }

          // If no encryptedBody, try to get message directly
          this.toastr.error(errorData.message || errorData.error || `Failed to send OTP to ${type}`);
        } catch (parseError) {
          console.log('Error parsing failed:', parseError);
          // Final fallback - try to get any message from the error object
          const errorMessage = error.error?.message || error.message || `Failed to send OTP to ${type}. Please try again.`;
          this.toastr.error(errorMessage);
        }
      }
    });
  }

  onOTPInput(type: 'mobile' | 'email', event: any): void {
    const value = event.target.value;
    if (value.length === 6) {
      this.validateOTP(type, value);
    }
  }

  validateOTP(type: 'mobile' | 'email', otp: string): void {
    const contactValue = type === 'mobile' ? this.step1Form.get('cellphone')?.value : this.step1Form.get('email')?.value;

    // Use the exact same payload structure as login component (but with isSignup: true)
    const payload = type === 'email' ?
      { email: contactValue, otp: otp, isSignup: true } :
      { mobileNo: contactValue, otp: otp, isSignup: true };

    // Use the exact same validation methods as login component
    const validationCall = type === 'email' ?
      this.userService.validateEmailOTPForLogin(payload) :
      this.userService.validateOTPForLogin(payload);

    validationCall.subscribe({
      next: (response: any) => {
        console.log('Signup OTP Validation Response:', response);

        try {
          // First try to parse the response directly (for email OTP which returns plain JSON)
          let result;
          try {
            result = typeof response === 'string' ? JSON.parse(response) : response;
          } catch (directParseError) {
            // If direct parsing fails, try decryption
            const decrypted = this.utility.decrypt(response.encryptedBody || response);
            result = JSON.parse(decrypted);
          }

          // Check if API response indicates success
          if (result.success === false || result.error === true) {
            // API returned error - show backend message and stop
            this.toastr.error(result.message || 'Wrong OTP entered. Please retry!');
            // Clear the OTP field
            this.step1Form.get(type === 'mobile' ? 'mobileOTP' : 'emailOTP')?.setValue('');
            return;
          }

          // Success case - mark as verified
          if (type === 'mobile') {
            this.mobileOTPVerified = true;
          } else {
            this.emailOTPVerified = true;
          }
          this.toastr.success(`${type === 'mobile' ? 'Mobile' : 'Email'} verified successfully!`);

        } catch (error) {
          console.log('Failed to parse signup OTP validation response:', error);
          // If all parsing fails, show generic error and clear field
          this.toastr.error('Wrong OTP entered. Please try again.');
          this.step1Form.get(type === 'mobile' ? 'mobileOTP' : 'emailOTP')?.setValue('');
        }
      },
      error: (error: any) => {
        console.log('Signup OTP Validation Error:', error);

        try {
          // Parse the error.error string to get the encryptedBody
          const errorData = typeof error.error === 'string' ? JSON.parse(error.error) : error.error;

          if (errorData && errorData.encryptedBody) {
            // Try to decrypt the encrypted error response
            try {
              const decryptedErr = this.utility.decryptStringError(errorData.encryptedBody);
              const parsedErr = JSON.parse(decryptedErr);
              // Show the actual error message from backend
              this.toastr.error(parsedErr.message || parsedErr.error || 'Invalid OTP');
            } catch (decryptError) {
              console.log('Decryption failed:', decryptError);
              // Try alternative decryption method
              try {
                const alternativeDecrypted = this.utility.decryptErrorMessage(error);
                this.toastr.error(alternativeDecrypted || 'Invalid OTP');
              } catch (altError) {
                console.log('Alternative decryption failed:', altError);
                // Final fallback
                this.toastr.error('Invalid OTP. Please try again.');
              }
            }
          } else {
            // If no encryptedBody, try to get message directly
            this.toastr.error(errorData.message || errorData.error || 'Invalid OTP');
          }
        } catch (parseError) {
          console.log('Error parsing failed:', parseError);
          // Final fallback - try to get any message from the error object
          const errorMessage = error.error?.message || error.message || 'Invalid OTP. Please try again.';
          this.toastr.error(errorMessage);
        }

        // Clear the OTP field
        this.step1Form.get(type === 'mobile' ? 'mobileOTP' : 'emailOTP')?.setValue('');
      }
    });
  }

  onFileSelect(event: any, type: string): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        this.toastr.error('Please upload only PDF, JPG, or PNG files');
        return;
      }

      // Validate file size (10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        this.toastr.error('File size should not exceed 10MB');
        return;
      }

      this.uploadedFiles[type] = file;
      const docType = this.documentTypes.find(doc => doc.key === type);
      this.toastr.success(`${docType?.name || type} uploaded successfully`);
    }
  }

  removeFile(type: string): void {
    delete this.uploadedFiles[type];
    const docType = this.documentTypes.find(doc => doc.key === type);
    this.toastr.info(`${docType?.name || type} removed`);
  }

  getShortFileName(fileName: string): string {
    if (fileName.length <= 20) {
      return fileName;
    }
    const extension = fileName.split('.').pop();
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    const shortName = nameWithoutExt.substring(0, 15) + '...';
    return shortName + '.' + extension;
  }

  triggerFileInput(docKey: string): void {
    const fileInput = document.getElementById(docKey + 'Input') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  private handleDecryptedError(error: any): void {
    try {
      let errorMessage = 'Registration failed. Please try again.';

      if (error.error) {
        try {
          // First try to parse the error as JSON
          const errorData = typeof error.error === 'string' ? JSON.parse(error.error) : error.error;

          // Check if it has encryptedBody property
          if (errorData.encryptedBody) {
            try {
              const decryptedError = this.utility.decrypt(errorData.encryptedBody);
              const parsedDecryptedError = typeof decryptedError === 'string' ? JSON.parse(decryptedError) : decryptedError;
              errorMessage = parsedDecryptedError.message || errorMessage;
            } catch (decryptError) {
              console.log('Error decrypting error message:', decryptError);
              // If decryption fails, try to get message from the outer error
              errorMessage = errorData.message || errorMessage;
            }
          } else {
            // No encryption, get message directly
            errorMessage = errorData.message || errorMessage;
          }
        } catch (parseError) {
          console.log('Error parsing error response:', parseError);
          // If all parsing fails, use the raw error
          errorMessage = error.message || errorMessage;
        }
      }

      this.toastr.error(errorMessage);
    } catch (generalError) {
      console.log('General error handling failed:', generalError);
      this.toastr.error('Registration failed. Please try again.');
    }
  }

  onSubmit(): void {
    // Validate that all required documents are uploaded
    if (!this.uploadedFiles['rfcDoc'] || !this.uploadedFiles['idDoc'] || !this.uploadedFiles['addressDoc']) {
      this.toastr.error('Please upload all required documents (RFC, Identity, and Address)');
      return;
    }

    // Validate that OTPs are verified
    // if (!this.mobileOTPVerified || !this.emailOTPVerified) {
    //   this.toastr.error('Please verify both mobile number and email');
    //   return;
    // }

    // Prepare customer data according to API structure
    const customerData = {
      firstName: this.step1Form.value.firstName,
      lastName: this.step1Form.value.lastName,
      email: this.step1Form.value.email,
      mobileNo: this.step1Form.value.cellphone,
      company: {
        name: this.step1Form.value.companyName
      },
      address: {
        name: this.step2Form.value.address
      },
      state: {
        id: parseInt(this.step2Form.value.state) || 1
      },
      city: {
        id: parseInt(this.step2Form.value.city) || 1
      },
      region: {
        id: parseInt(this.step2Form.value.region) || 1
      },
      crop: {
        id: parseInt(this.step2Form.value.mainCrop) || 1
      },
      zone: {
        id: parseInt(this.step2Form.value.zone) || 1
      }
    };

    console.log('Signup data:', customerData);

    // Call signup API
    this.userService.customerSignup(
      customerData,
      this.uploadedFiles['rfcDoc'],
      this.uploadedFiles['idDoc'],
      this.uploadedFiles['addressDoc']
    ).subscribe({
      next: (response: any) => {
        console.log('Signup response:', response);
        try {
          // Try to decrypt and parse response
          let result;
          try {
            const decryptedResponse = this.utility.decrypt(response);
            result = typeof decryptedResponse === 'string' ? JSON.parse(decryptedResponse) : decryptedResponse;
          } catch (parseError) {
            // If decryption/parsing fails, try direct parsing
            try {
              result = typeof response === 'string' ? JSON.parse(response) : response;
            } catch (directParseError) {
              // If all parsing fails, assume success
              result = { success: true };
            }
          }

          if (result.success === false || result.error === true) {
            this.toastr.error(result.message || 'Registration failed. Please try again.');
            return;
          }

          this.toastr.success('Registration submitted successfully!');
          this.close.emit();
        } catch (error) {
          console.log('Error processing signup response:', error);
          this.toastr.success('Registration submitted successfully!');
          this.close.emit();
        }
      },
      error: (error: any) => {
        console.log('Signup error:', error);
        this.handleDecryptedError(error);
      }
    });
  }

  onOverlayClick(event: Event): void {
    this.close.emit();
  }

  // ================== DROPDOWN API METHODS ==================

  // Get all regions (public API for signup)
  getRegion(): void {
    this.regionDataList = [];
    this.userService.getRegionPublic().subscribe({
      next: (response: any) => {
        try {
          const decryptedResponse = this.utility.decryptString(response);
          const regionArray = JSON.parse(decryptedResponse);
          this.regionDataList = regionArray.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error loading regions:', error);
          this.toastr.error('Failed to load regions');
        }
      },
      error: (error) => {
        console.error('Error fetching regions:', error);
        this.toastr.error('Failed to load regions');
      }
    });
  }

  // Get zones by region (public API for signup)
  getZoneByID(regionId: any): void {
    const data = { regionId: regionId };
    this.zoneDataList = [];
    this.userService.getZoneByIdPublic(data).subscribe({
      next: (response: any) => {
        try {
          const decryptedResponse = this.utility.decryptString(response);
          const zoneArray = JSON.parse(decryptedResponse);
          this.zoneDataList = zoneArray.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error loading zones:', error);
          this.toastr.error('Failed to load zones');
        }
      },
      error: (error) => {
        console.error('Error fetching zones:', error);
        this.toastr.error('Failed to load zones');
      }
    });
  }

  // Get states by zone (public API for signup)
  getStateByID(zoneId: any): void {
    const data = { stateId: zoneId };
    this.stateDataList = [];
    this.userService.getStateByIDPublic(data).subscribe({
      next: (response: any) => {
        try {
          const decryptedResponse = this.utility.decryptString(response);
          const stateArray = JSON.parse(decryptedResponse);
          this.stateDataList = stateArray.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error loading states:', error);
          this.toastr.error('Failed to load states');
        }
      },
      error: (error) => {
        console.error('Error fetching states:', error);
        this.toastr.error('Failed to load states');
      }
    });
  }

  // Get cities by state (public API for signup)
  getCityByID(stateId: any): void {
    const data = { stateId: stateId };
    this.cityDataList = [];
    this.userService.getCityByIdPublic(data).subscribe({
      next: (response: any) => {
        try {
          const decryptedResponse = this.utility.decryptString(response);
          const cityArray = JSON.parse(decryptedResponse);
          this.cityDataList = cityArray.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error loading cities:', error);
          this.toastr.error('Failed to load cities');
        }
      },
      error: (error) => {
        console.error('Error fetching cities:', error);
        this.toastr.error('Failed to load cities');
      }
    });
  }

  // Get crops by zone (public API for signup)
  getCropByID(zoneId: any): void {
    const data = { zoneId: zoneId };
    this.cropDataList = [];
    this.userService.getCropByIdPublic(data).subscribe({
      next: (response: any) => {
        try {
          const decryptedResponse = this.utility.decryptString(response);
          const cropArray = JSON.parse(decryptedResponse);
          this.cropDataList = cropArray.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error loading crops:', error);
          this.toastr.error('Failed to load crops');
        }
      },
      error: (error) => {
        console.error('Error fetching crops:', error);
        this.toastr.error('Failed to load crops');
      }
    });
  }

  // ================== MULTISELECT DROPDOWN HANDLERS ==================

  // Region selection
  selectRegion(event: any): void {
    console.log('Region selected:', event);
    this.selectedRegion = [event]; // Keep as array for multiselect

    // Clear dependent dropdowns
    this.selectedZone = [];
    this.selectedState = [];
    this.selectedCity = [];
    this.selectedCrop = [];
    this.zoneDataList = [];
    this.stateDataList = [];
    this.cityDataList = [];
    this.cropDataList = [];

    // Update form control
    this.step2Form.patchValue({
      region: event.id,
      zone: '',
      state: '',
      city: '',
      mainCrop: ''
    });

    // Load zones for selected region
    this.getZoneByID(event.id);
  }

  deselectionRegion(event: any): void {
    console.log('Region deselected:', event);
    this.selectedRegion = [];
    this.selectedZone = [];
    this.selectedState = [];
    this.selectedCity = [];
    this.selectedCrop = [];
    this.zoneDataList = [];
    this.stateDataList = [];
    this.cityDataList = [];
    this.cropDataList = [];

    this.step2Form.patchValue({
      region: '',
      zone: '',
      state: '',
      city: '',
      mainCrop: ''
    });
  }

  deselectionAllRegion(event: any): void {
    this.deselectionRegion(event);
  }

  // Zone selection
  selectZone(event: any): void {
    console.log('Zone selected:', event);
    this.selectedZone = [event]; // Keep as array for multiselect

    // Clear dependent dropdowns
    this.selectedState = [];
    this.selectedCity = [];
    this.selectedCrop = [];
    this.stateDataList = [];
    this.cityDataList = [];
    this.cropDataList = [];

    // Update form control
    this.step2Form.patchValue({
      zone: event.id,
      state: '',
      city: '',
      mainCrop: ''
    });

    // Load states and crops for selected zone
    this.getStateByID(event.id);
    this.getCropByID(event.id);
  }

  deselectionZone(event: any): void {
    console.log('Zone deselected:', event);
    this.selectedZone = [];
    this.selectedState = [];
    this.selectedCity = [];
    this.selectedCrop = [];
    this.stateDataList = [];
    this.cityDataList = [];
    this.cropDataList = [];

    this.step2Form.patchValue({
      zone: '',
      state: '',
      city: '',
      mainCrop: ''
    });
  }

  deselectionAllZone(event: any): void {
    this.deselectionZone(event);
  }

  // State selection
  selectState(event: any): void {
    console.log('State selected:', event);
    this.selectedState = [event]; // Keep as array for multiselect

    // Clear dependent dropdowns
    this.selectedCity = [];
    this.cityDataList = [];

    // Update form control
    this.step2Form.patchValue({
      state: event.id,
      city: ''
    });

    // Load cities for selected state
    this.getCityByID(event.id);
  }

  deselectionState(event: any): void {
    console.log('State deselected:', event);
    this.selectedState = [];
    this.selectedCity = [];
    this.cityDataList = [];

    this.step2Form.patchValue({
      state: '',
      city: ''
    });
  }

  deselectionAllState(event: any): void {
    this.deselectionState(event);
  }

  // City selection
  selectCity(event: any): void {
    console.log('City selected:', event);
    this.selectedCity = [event]; // Keep as array for multiselect
    this.step2Form.patchValue({
      city: event.id
    });
  }

  deselectionCity(event: any): void {
    console.log('City deselected:', event);
    this.selectedCity = [];
    this.step2Form.patchValue({
      city: ''
    });
  }

  deselectionAllCity(event: any): void {
    this.deselectionCity(event);
  }

  // Crop selection
  selectCrop(event: any): void {
    console.log('Crop selected:', event);
    this.selectedCrop = [event]; // Keep as array for multiselect
    this.step2Form.patchValue({
      mainCrop: event.id
    });
  }

  deselectionCrop(event: any): void {
    console.log('Crop deselected:', event);
    this.selectedCrop = [];
    this.step2Form.patchValue({
      mainCrop: ''
    });
  }

  deselectionAllCrop(event: any): void {
    this.deselectionCrop(event);
  }

  // Input validation methods
  onNameInput(event: any): void {
    // Allow letters, numbers, and spaces for name fields
    const value = event.target.value;
    const sanitized = value.replace(/[^a-zA-Z0-9\s]/g, '');
    if (value !== sanitized) {
      event.target.value = sanitized;
      const controlName = event.target.getAttribute('formControlName');
      if (controlName) {
        this.step1Form.get(controlName)?.setValue(sanitized);
      }
    }
  }

  onNumberInput(event: any): void {
    // Allow only numbers for phone fields
    const value = event.target.value;
    const sanitized = value.replace(/[^0-9]/g, '');
    if (value !== sanitized) {
      event.target.value = sanitized;
      const controlName = event.target.getAttribute('formControlName');
      if (controlName) {
        this.step1Form.get(controlName)?.setValue(sanitized);
      }
    }
  }

  onCompanyNameInput(event: any): void {
    // Allow letters, numbers, spaces, and common company characters
    const value = event.target.value;
    const sanitized = value.replace(/[^a-zA-Z0-9\s&.-]/g, '');
    if (value !== sanitized) {
      event.target.value = sanitized;
      this.step1Form.get('companyName')?.setValue(sanitized);
    }
  }

  onEmailInput(event: any): boolean {
    const allowedKeys = [64, 46, 45, 95]; // @, ., -, _
    const k = event.charCode || event.keyCode;

    // Prevent space at the beginning
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
      return false;
    }

    // Prevent consecutive spaces
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
      return false;
    }

    // Allow letters, numbers, and specific symbols for email
    if (
      (k >= 65 && k <= 90) ||  // A-Z
      (k >= 97 && k <= 122) || // a-z
      (k >= 48 && k <= 57) ||  // 0-9
      allowedKeys.includes(k)   // @, ., -, _
    ) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }
}
