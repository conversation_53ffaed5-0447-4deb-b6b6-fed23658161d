.signup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.signup-modal {
  background: white;
  border-radius: 12px;
  width: 95%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.signup-header {
  background: #FF8033;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px 12px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .back-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 1rem;
  }

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
  }
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem 1rem;

  .step {
    display: flex;
    align-items: center;
    justify-content: center;

    .step-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: #E5E7EB;
      color: #9CA3AF;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      font-size: 1.5rem;
      font-weight: 600;

      svg {
        width: 32px;
        height: 32px;
      }
    }

    &.active .step-icon {
      background: #FF8033;
      color: white;
    }

    &.completed .step-icon {
      background: #FF8033;
      color: white;
    }
  }

  .step-line {
    width: 140px;
    height: 4px;
    background: #E5E7EB;
    transition: background-color 0.3s ease;

    &.completed {
      background: #FF8033;
    }
  }
}

.step-content {
  padding: 0 1.5rem 1rem;
}

.step-form {
  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }
  }

  .form-group {
    flex: 1;

    &.full-width {
      flex: 1 1 100%;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #000000;
      font-size: 0.9rem;

      .required {
        color: #EF4444;
        margin-left: 2px;
      }
    }

    .form-input,
    .form-select {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #E5E7EB; // ✅ Thicker border like name fields
      border-radius: 6px;
      font-size: 0.9rem;
      color: #000000;
      font-weight: 500;
      transition: border-color 0.2s;
      background-color: #FFFFFF;

      &:focus {
        outline: none;
        border-color: #FF8033;
        box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
      }

      &::placeholder {
        color: #9CA3AF;
      }
    }

    .input-with-verify {
      display: flex;
      gap: 0.5rem;

      .form-input {
        flex: 1;
      }

      .verify-btn {
        background: #FF8033;
        color: white;
        border: none;
        padding: 0.75rem 1rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        white-space: nowrap;



        &:disabled {
          background: #D1D5DB;
          cursor: not-allowed;
        }
      }
    }

    .otp-field {
      margin-top: 0.5rem;
      display: flex;
      gap: 0.5rem;

      .otp-input {
        flex: 1;
        font-size: 0.9rem;
      }

      .resend-btn {
        background: transparent;
        color: #FF8033;
        border: 1px solid #FF8033;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;
      }
    }

    .error-message {
      color: #DC2626;
      font-size: 0.8rem;
      margin-top: 0.25rem;
      display: block;
    }

    .uploaded-file {
      color: #10B981;
      font-size: 0.8rem;
      margin-top: 0.5rem;
      font-weight: 500;
    }

    .uploaded-file-info {
      text-align: center;
      padding: 1rem;

      .file-name {
        color: #10B981;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        font-size: 0.9rem;
      }

      .file-size {
        color: #6B7280;
        font-size: 0.75rem;
        display: block;
        margin-bottom: 0.5rem;
      }

      .remove-file-btn {
        background: #EF4444;
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin: 0 auto;
        transition: background-color 0.2s;

        &:hover {
          background: #DC2626;
        }
      }
    }

    .upload-icon.uploaded {
      .upload-icon svg path {
        stroke: #10B981;
      }
    }
  }
}

.upload-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  padding: 1rem 0;
  max-width: 100%;

  .upload-card {
    border: 2px dashed #FF8033;
    border-radius: 12px;
    padding: 1.5rem 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #FFF5F0;

    &:hover {
      border-color: #E85D04;
      background-color: #FFF0E6;
    }

    .upload-icon {
      margin-bottom: 1rem;
      display: flex;
      justify-content: center;

      svg {
        width: 48px;
        height: 48px;
      }

      &.uploaded {
        svg path {
          stroke: #6B7280;
        }
      }
    }

    .upload-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .upload-text {
      text-align: center;

      .upload-title {
        margin: 0 0 0.5rem;
        font-size: 0.9rem;
        color: #374151;
        font-weight: 500;
        line-height: 1.3;

        .browse-link {
          color: #FF8033;
          font-weight: 600;
          text-decoration: underline;
        }
      }

      .upload-description {
        color: #6B7280;
        font-size: 0.75rem;
        display: block;
        margin-top: 0.25rem;
      }
    }

    .uploaded-file-info {
      width: 100%;
      max-width: 200px;

      .file-display {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: white;
        border: 1px solid #E5E7EB;
        border-radius: 8px;
        padding: 10px 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .file-name-only {
          font-size: 0.85rem;
          color: #6B7280;
          font-weight: 500;
          flex: 1;
          text-align: left;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 8px;
          max-width: 140px;
        }

        .remove-file-btn {
          background: none;
          border: none;
          color: #EF4444;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background-color 0.2s;

          &:hover {
            background-color: #FEE2E2;
          }

          svg {
            width: 14px;
            height: 14px;
          }
        }
      }
    }

    .file-input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }
  }
}

.signup-footer {
  padding: 1rem 1.5rem 1.5rem;
  display: flex;
  justify-content: center;

  .next-btn {
    background: #FF8033;
    color: white;
    border: none;
    padding: 0.75rem 3rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 120px;

    &:disabled {
      background: #D1D5DB;
      cursor: not-allowed;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .signup-modal {
    width: 95%;
    margin: 1rem;
  }

  .progress-steps {
    padding: 1.5rem 1rem 1rem;

    .step .step-icon {
      width: 50px;
      height: 50px;
    }

    .step-line {
      width: 60px;
    }
  }

  .upload-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .upload-card {
    min-height: 120px;
    padding: 1rem 0.75rem;

    .upload-icon svg {
      width: 40px;
      height: 40px;
    }

    .upload-text .upload-title {
      font-size: 0.8rem;
    }

    .upload-text .upload-description {
      font-size: 0.7rem;
    }
  }
}

// ================== USER MANAGEMENT DROPDOWN STYLING ==================

// Import user management styles for consistent dropdown appearance
::ng-deep .myclass.customer-dd {
  .cuppa-dropdown {
    border: 1px solid #CCC;
    border-radius: 4px;
    min-height: 42px; // ✅ Increased height to match address field

    .selected-list {
      .c-btn {
        border-color: #CCC !important;
        border-radius: 4px;
        padding: 10px 12px; // ✅ Increased padding for better height
        min-height: 42px; // ✅ Match address field height
        display: flex;
        align-items: center;
        font-size: 0.9rem;

        .c-list {
          .c-token {
            background-color: #FF8033;
            border-radius: 2px;
            color: white;
            padding: 4px 22px 4px 8px;
            margin-right: 4px;
            position: relative;

            .c-label {
              display: block;
              float: left;
              position: relative;
              top: 0;
            }

            .c-remove {
              position: absolute;
              right: 8px;
              top: 50%;
              transform: translateY(-50%);
              color: white;
              cursor: pointer;
            }
          }
        }
      }
    }

    .dropdown-list {
      border: 1px solid #CCC;
      border-top: none;
      max-height: none; // ✅ Remove fixed height for dynamic sizing
      background: white;
      z-index: 1000;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden; // ✅ Hide outer overflow completely

      .filter-textbox {
        padding: 8px;
        border-bottom: 1px solid #eee;
        flex-shrink: 0;

        input {
          width: 100%;
          border: 1px solid #CCC;
          border-radius: 4px;
          padding: 6px 8px;
          font-size: 14px;
          outline: none;

          &:focus {
            border-color: #FF8033;
          }
        }
      }

      .list-area {
        max-height: 150px; // ✅ Dynamic height based on content
        overflow-y: auto; // ✅ Single scrollbar only here
        overflow-x: hidden;

        .pure-checkbox {
          padding: 8px 12px; // ✅ Compact padding
          cursor: pointer;
          border: none !important;
          margin: 0;
          display: block;

          &:hover {
            background-color: #f8f9fa;
          }

          &.selected {
            background-color: #e3f2fd;
          }

          input[type="checkbox"] {
            display: none !important;
          }

          label {
            cursor: pointer;
            margin: 0;
            font-weight: normal;
            color: #333;
            font-size: 14px;
            width: 100%;
            display: block;
            padding: 0;
            line-height: 1.4;
          }
        }

        // ✅ Clean single scrollbar
        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }
      }
    }
  }
}

// Additional styling for form groups with multiselect
.form-group {
  angular2-multiselect {
    width: 100%;
    display: block;

    .cuppa-dropdown {
      width: 100%;
    }
  }
}

// ✅ Additional fixes for clean dropdown appearance
::ng-deep .myclass.customer-dd .cuppa-dropdown .dropdown-list {
  // Remove any remaining borders and lines
  .list-area {
    border: none !important;

    ul {
      border: none !important;
      margin: 0;
      padding: 0;
      list-style: none;
    }

    li {
      border: none !important;
      border-bottom: none !important;
      border-top: none !important;
      margin: 0;

      &.pure-checkbox {
        border: none !important;
        border-bottom: none !important;
        border-top: none !important;

        &:not(:last-child) {
          border-bottom: none !important;
        }

        &:first-child {
          border-top: none !important;
        }
      }
    }
  }

  // Ensure no double scrollbars
  .list-area {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
  }
}

// ✅ Match address field height exactly
.form-input {
  min-height: 42px !important;
  padding: 10px 12px !important;
  box-sizing: border-box;
}
