<div class="signup-overlay" (click)="onOverlayClick($event)">
  <div class="signup-modal" (click)="$event.stopPropagation()">
    <!-- Header -->
    <div class="signup-header">
      <button class="back-btn" (click)="onBack()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <h2>Leader SignUp</h2>
    </div>

    <!-- Progress Steps -->
    <div class="progress-steps">
      <div class="step" [class.active]="currentStep >= 1" [class.completed]="currentStep > 1">
        <div class="step-icon">
          <img [src]="currentStep >= 1 ? 'assets/img/fill-contacts.svg' : 'assets/img/unfill-contact.svg'" alt="Contact Step" width="35" height="35">
        </div>
      </div>
      <div class="step-line" [class.completed]="currentStep > 1"></div>
      <div class="step" [class.active]="currentStep >= 2" [class.completed]="currentStep > 2">
        <div class="step-icon">
          <img [src]="currentStep >= 2 ? 'assets/img/fill-addresses.svg' : 'assets/img/unfill-address.svg'" alt="Address Step" width="35" height="35">
        </div>
      </div>
      <div class="step-line" [class.completed]="currentStep > 2"></div>
      <div class="step" [class.active]="currentStep >= 3">
        <div class="step-icon">
          <img [src]="currentStep >= 3 ? 'assets/img/fill-documents.svg' : 'assets/img/unfill-document.svg'" alt="Document Step" width="35" height="35">
        </div>
      </div>
    </div>

    <!-- Step Content -->
    <div class="step-content">
      <!-- Step 1: Personal Information -->
      <div *ngIf="currentStep === 1" class="step-form">
        <form [formGroup]="step1Form">
          <div class="form-row">
            <div class="form-group">
              <label>First Name<span class="required">*</span></label>
              <input type="text" formControlName="firstName" placeholder="Enter First Name" class="form-input" maxlength="50" (input)="onNameInput($event)">
              <div *ngIf="step1Form.get('firstName')?.invalid && step1Form.get('firstName')?.touched" class="error-message">
                <span *ngIf="step1Form.get('firstName')?.errors?.['required']">First name is required</span>
                <span *ngIf="step1Form.get('firstName')?.errors?.['pattern']">Only letters, numbers and spaces are allowed</span>
                <span *ngIf="step1Form.get('firstName')?.errors?.['minlength']">Minimum 2 characters required</span>
              </div>
            </div>
            <div class="form-group">
              <label>Last Name<span class="required">*</span></label>
              <input type="text" formControlName="lastName" placeholder="Enter Last Name" class="form-input" maxlength="50" (input)="onNameInput($event)">
              <div *ngIf="step1Form.get('lastName')?.invalid && step1Form.get('lastName')?.touched" class="error-message">
                <span *ngIf="step1Form.get('lastName')?.errors?.['required']">Last name is required</span>
                <span *ngIf="step1Form.get('lastName')?.errors?.['pattern']">Only letters, numbers and spaces are allowed</span>
                <span *ngIf="step1Form.get('lastName')?.errors?.['minlength']">Minimum 2 characters required</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>Cellphone Number<span class="required">*</span></label>
              <div class="input-with-verify">
                <input type="text" formControlName="cellphone" placeholder="Enter Cellphone Number" class="form-input" maxlength="10" (input)="onNumberInput($event)">
                <button type="button" class="verify-btn" [disabled]="!step1Form.get('cellphone')?.valid" (click)="sendOTP('mobile')">Verify</button>
              </div>
              <div *ngIf="step1Form.get('cellphone')?.invalid && step1Form.get('cellphone')?.touched" class="error-message">
                <span *ngIf="step1Form.get('cellphone')?.errors?.['required']">Mobile number is required</span>
                <span *ngIf="step1Form.get('cellphone')?.errors?.['pattern']">Please enter a valid 10-digit mobile number</span>
              </div>
              <div *ngIf="mobileOTPSent" class="otp-field">
                <input type="text" formControlName="mobileOTP" placeholder="Enter OTP" class="form-input otp-input" maxlength="6" (input)="onOTPInput('mobile', $event)">
                <button type="button" class="resend-btn" (click)="sendOTP('mobile')">Resend OTP</button>
              </div>
              <div *ngIf="step1Form.get('mobileOTP')?.invalid && step1Form.get('mobileOTP')?.touched" class="error-message">
                <span *ngIf="step1Form.get('mobileOTP')?.errors?.['required']">OTP is required</span>
                <span *ngIf="step1Form.get('mobileOTP')?.errors?.['pattern']">Please enter a valid 6-digit OTP</span>
              </div>
            </div>
            <div class="form-group">
              <label>Email<span class="required">*</span></label>
              <div class="input-with-verify">
                <input type="email" formControlName="email" placeholder="Enter Email" class="form-input">
                <button type="button" class="verify-btn" [disabled]="!step1Form.get('email')?.valid" (click)="sendOTP('email')">Verify</button>
              </div>
              <div *ngIf="step1Form.get('email')?.invalid && step1Form.get('email')?.touched" class="error-message">
                <span *ngIf="step1Form.get('email')?.errors?.['required']">Email is required</span>
                <span *ngIf="step1Form.get('email')?.errors?.['email']">Please enter a valid email address</span>
              </div>
              <div *ngIf="emailOTPSent" class="otp-field">
                <input type="text" formControlName="emailOTP" placeholder="Enter OTP" class="form-input otp-input" maxlength="6" (input)="onOTPInput('email', $event)">
                <button type="button" class="resend-btn" (click)="sendOTP('email')">Resend OTP</button>
              </div>
              <div *ngIf="step1Form.get('emailOTP')?.invalid && step1Form.get('emailOTP')?.touched" class="error-message">
                <span *ngIf="step1Form.get('emailOTP')?.errors?.['required']">OTP is required</span>
                <span *ngIf="step1Form.get('emailOTP')?.errors?.['pattern']">Please enter a valid 6-digit OTP</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>Company Name<span class="required">*</span></label>
              <input type="text" formControlName="companyName" placeholder="Enter Company Name" class="form-input" maxlength="100" (input)="onCompanyNameInput($event)">
              <div *ngIf="step1Form.get('companyName')?.invalid && step1Form.get('companyName')?.touched" class="error-message">
                <span *ngIf="step1Form.get('companyName')?.errors?.['required']">Company name is required</span>
                <span *ngIf="step1Form.get('companyName')?.errors?.['minlength']">Minimum 2 characters required</span>
                <span *ngIf="step1Form.get('companyName')?.errors?.['pattern']">Only letters, numbers, spaces and common symbols are allowed</span>
              </div>
            </div>
            <div class="form-group">
              <!-- Empty div to maintain two-column layout -->
            </div>
          </div>
        </form>
      </div>

      <!-- Step 2: Address Information -->
      <div *ngIf="currentStep === 2" class="step-form">
        <form [formGroup]="step2Form">
          <div class="form-row">
            <div class="form-group">
              <label>Address<span class="required">*</span></label>
              <input type="text" formControlName="address" placeholder="Enter Address" class="form-input" maxlength="200">
              <div *ngIf="step2Form.get('address')?.invalid && step2Form.get('address')?.touched" class="error-message">
                <span *ngIf="step2Form.get('address')?.errors?.['required']">Address is required</span>
                <span *ngIf="step2Form.get('address')?.errors?.['minlength']">Minimum 5 characters required</span>
              </div>
            </div>
            <div class="form-group">
              <label>Region<span class="required">*</span></label>
              <angular2-multiselect
                [data]="regionDataList"
                [(ngModel)]="selectedRegion"
                [settings]="regionDropdownSettings"
                (onSelect)="selectRegion($event)"
                (onDeSelect)="deselectionRegion($event)"
                (onDeSelectAll)="deselectionAllRegion($event)"
                [ngModelOptions]="{ standalone: true }">
              </angular2-multiselect>
              <div *ngIf="step2Form.get('region')?.invalid && step2Form.get('region')?.touched" class="error-message">
                <span *ngIf="step2Form.get('region')?.errors?.['required']">Region is required</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>Zone or Area<span class="required">*</span></label>
              <angular2-multiselect
                [data]="zoneDataList"
                [(ngModel)]="selectedZone"
                [settings]="zoneDropdownSettings"
                (onSelect)="selectZone($event)"
                (onDeSelect)="deselectionZone($event)"
                (onDeSelectAll)="deselectionAllZone($event)"
                [ngModelOptions]="{ standalone: true }">
              </angular2-multiselect>
              <div *ngIf="step2Form.get('zone')?.invalid && step2Form.get('zone')?.touched" class="error-message">
                <span *ngIf="step2Form.get('zone')?.errors?.['required']">Zone is required</span>
              </div>
            </div>
            <div class="form-group">
              <label>State<span class="required">*</span></label>
              <angular2-multiselect
                [data]="stateDataList"
                [(ngModel)]="selectedState"
                [settings]="stateDropdownSettings"
                (onSelect)="selectState($event)"
                (onDeSelect)="deselectionState($event)"
                (onDeSelectAll)="deselectionAllState($event)"
                [ngModelOptions]="{ standalone: true }">
              </angular2-multiselect>
              <div *ngIf="step2Form.get('state')?.invalid && step2Form.get('state')?.touched" class="error-message">
                <span *ngIf="step2Form.get('state')?.errors?.['required']">State is required</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>City<span class="required">*</span></label>
              <angular2-multiselect
                [data]="cityDataList"
                [(ngModel)]="selectedCity"
                [settings]="cityDropdownSettings"
                (onSelect)="selectCity($event)"
                (onDeSelect)="deselectionCity($event)"
                (onDeSelectAll)="deselectionAllCity($event)"
                [ngModelOptions]="{ standalone: true }">
              </angular2-multiselect>
              <div *ngIf="step2Form.get('city')?.invalid && step2Form.get('city')?.touched" class="error-message">
                <span *ngIf="step2Form.get('city')?.errors?.['required']">City is required</span>
              </div>
            </div>
            <div class="form-group">
              <label>Main Crop<span class="required">*</span></label>
              <angular2-multiselect
                [data]="cropDataList"
                [(ngModel)]="selectedCrop"
                [settings]="cropDropdownSettings"
                (onSelect)="selectCrop($event)"
                (onDeSelect)="deselectionCrop($event)"
                (onDeSelectAll)="deselectionAllCrop($event)"
                [ngModelOptions]="{ standalone: true }">
              </angular2-multiselect>
              <div *ngIf="step2Form.get('mainCrop')?.invalid && step2Form.get('mainCrop')?.touched" class="error-message">
                <span *ngIf="step2Form.get('mainCrop')?.errors?.['required']">Main crop is required</span>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Step 3: Document Upload -->
      <div *ngIf="currentStep === 3" class="step-form">
        <div class="upload-section">
          <div class="upload-card" *ngFor="let doc of documentTypes">
            <div class="upload-icon" [class.uploaded]="uploadedFiles[doc.key]">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" [attr.stroke]="uploadedFiles[doc.key] ? '#6B7280' : '#FF8033'" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M14 2V8H20" [attr.stroke]="uploadedFiles[doc.key] ? '#6B7280' : '#FF8033'" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M16 13H8M16 17H8M10 9H8" [attr.stroke]="uploadedFiles[doc.key] ? '#6B7280' : '#FF8033'" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="upload-content">
              <div *ngIf="!uploadedFiles[doc.key]" class="upload-text">
                <p class="upload-title">Upload {{ doc.name }}, <span class="browse-link">Browse</span></p>
                <small class="upload-description">Upload {{ doc.description.split('(')[0] }}</small>
              </div>
              <div *ngIf="uploadedFiles[doc.key]" class="uploaded-file-info">
                <div class="file-display">
                  <span class="file-name-only">{{ getShortFileName(uploadedFiles[doc.key].name) }}</span>
                  <button type="button" class="remove-file-btn" (click)="removeFile(doc.key)" title="Remove file">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                      <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <input type="file" class="file-input" (change)="onFileSelect($event, doc.key)" accept=".pdf,.jpg,.jpeg,.png">
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="signup-footer">
      <button class="next-btn" (click)="onNext()" [disabled]="!isCurrentStepValid()">
        {{ currentStep === 3 ? 'Submit' : 'Next' }}
      </button>
    </div>
  </div>
</div>
